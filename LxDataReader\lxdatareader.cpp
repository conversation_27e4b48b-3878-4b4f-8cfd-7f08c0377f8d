#include "lxdatareader.h"
#include <QMutableMapIterator>
#include "FileData/avgmassmanager.h"

LxDataReader::LxDataReader(QObject *parent) : QObject{parent}
{
    qDebug() << "LxDataReader: 初始化统一数据读取器";
}

LxDataReader::~LxDataReader()
{
    qDebug() << "LxDataReader: 析构函数";
}

bool LxDataReader::loadFileTicFullPage(FileData *data)
{
    QString pFilePath = data->getFilePath();
    if (pFilePath.isEmpty())
    {
        return false;
    }
    QList<std::vector<double>> pStructLines;

    // 先加载struct _StreamHead
    QByteArray pStreamHead;

    _StreamHead *tmpStreamHead;
    if (pFilePath.isEmpty())
        return false;
    qint64 sizeHead = sizeof(_StreamHead);
    QFile tmpFile(pFilePath);
    pStreamHead.resize(sizeHead);
    try
    {
        if (!tmpFile.open(QIODevice::ReadOnly))
            return false;
        tmpFile.seek(0);
    }
    catch (...)
    {
        return false;
    }
    try
    {
        if (sizeHead != tmpFile.read(pStreamHead.data(), sizeHead))
        {
            tmpFile.close();
            return false;
        }
        else
        {
            tmpStreamHead = (_StreamHead *)(pStreamHead.data());
            sizeHead = tmpStreamHead->length;
            pStreamHead.resize(sizeHead);
            tmpStreamHead = (_StreamHead *)(pStreamHead.data());
            tmpFile.seek(0);
            if (sizeHead != tmpFile.read(pStreamHead.data(), sizeHead))
            {
                tmpFile.close();
                return false;
            }
        }
    }
    catch (...)
    {
        return false;
    }
    // qDebugStreamHead(tmpStreamHead);
    // QList<cParamValue::_StreamHeadParam *> tmpList;
    // if (!_StreamHead::toList(pStreamHead, tmpList)) {
    //     return false;
    // }
    // foreach (cParamValue::_StreamHeadParam *param, tmpList) {
    //     qDebugStreamHeadParam(param);
    // }
    // qDebug().noquote() << "子属性长度：" << sizeof(tmpList) << tmpList.length();

    int numXIC = updateXIC(getNumXIC(pStreamHead));
    int numOtherLine = getNumOtherLine(getNumOtherLine(pStreamHead), pStructLines);
    // 暂时不启用这个代码，因为文件结构还没变，等到otherLine里有segNo eventNo experimentNo在启用
    if (false)
    {
        if (numOtherLine != 3)
        {
            // 如果numOtherLine小于3 则表示没有 segNo eventNo experimentNo的信息，读取失败
            qDebug() << "numOtherLine小于3，读取失败";
            return false;
        }
    }
    qint64 sizeData = tmpFile.size() - sizeHead;
    int sizeI64 = sizeof(qint64), sizeD = sizeof(double);
    if (sizeData <= 0)
    {
        // 如果数据部分大小小于等于0，清空所有输出参数并返回false
        qDebug() << "数据大小为0 读取失败";
        return false;
    }
    // 计算各数据偏移量：索引偏移、Y值偏移、XIC偏移和其他线偏移
    int offsetI = sizeI64 + sizeD * 2 + sizeD * numXIC + sizeD * numOtherLine;
    int offsetY = sizeI64 + sizeD;
    int offsetXIC = sizeI64 + sizeD + sizeD;
    int offsetOtherLine = offsetXIC + numXIC * sizeD;

    // cursor: 计算数据行数（总数据大小除以每行数据的大小）
    qint64 lineData = sizeData / (offsetI);
    QByteArray tempArray;
    tempArray.resize(offsetI * 3);
    try
    {
        if (offsetI * 3 != tmpFile.read(tempArray.data(), offsetI * 3))
        {
            tmpFile.close();
            return false;
        }
    }
    catch (...)
    {
        tmpFile.close();
        return false;
    }
    double interval = *((double *)(tempArray.data() + offsetI * 2 + sizeI64)) - *((double *)(tempArray.data() + offsetI + sizeI64));
    double allTime = ((double)sizeData) / ((double)offsetI) * interval;

    // 调整临时数组大小以适应所有数据
    tempArray.resize(sizeData);
    try
    {
        // cursor: 将文件指针定位到头部结束位置（数据起始位置）
        tmpFile.seek(sizeHead);
        // cursor: 尝试读取所有数据
        if (sizeData != tmpFile.read(tempArray.data(), sizeData))
        {
            // cursor: 如果读取的字节数不等于预期大小，关闭文件并返回false
            tmpFile.close();
            return false;
        }
    }
    catch (...)
    {
        // cursor: 捕获异常但不做处理（可能应该添加错误处理）
    }

    try
    {
        // cursor: 尝试关闭文件
        tmpFile.close();
    }
    catch (...)
    {
        // cursor: 捕获关闭文件时的异常，返回false
        return false;
    }
    // cursor: 初始化行偏移量为0
    int offsetLine = 0;

    // cursor: 设置每条结构线的大小为数据行数
    for (int j = 0; j < numOtherLine; ++j)
    {
        pStructLines[j].resize(lineData);
    }
    QVector<qint64> pIndexArray;
    for (qint64 i = 0; i < lineData; ++i)
    {
        // cursor: 计算当前行在字节数组中的偏移量
        offsetLine = i * offsetI;
        // cursor: 先遍历每条结构线，将数据从临时数组复制到结构线数组

        // 获取  segNo eventNo experimentNo
        if (true)
        {
            double ticX;
            double ticY;
            double experimentNo = 0.0; // 因为没有修改文件结构，所以默认只有一个experiment
            // 使用新的数据结构，移除Experiment概念
            // 直接使用TicChartData管理数据
            int eventId = static_cast<int>(experimentNo);
            TicChartData *ticData = data->getTicData(eventId);
            if (!ticData)
            {
                ticData = data->createTicData(eventId);
                qDebug() << "LxDataReader: 创建TIC数据，事件ID:" << eventId;
            }
            // TODO: 重构LxDataReader以使用新的TicChartData结构
            // 暂时注释掉旧的Experiment数据处理逻辑
            qDebug() << "LxDataReader: 需要重构数据处理逻辑以使用新的TicChartData结构";

            // 临时跳过数据处理，避免编译错误
            // 这部分需要重新设计以适配新的数据结构
        }
        else
        {
            // TODO: 重构LxDataReader的else分支以使用新的TicChartData结构
            qDebug() << "LxDataReader: else分支需要重构，暂时跳过";
            // 暂时注释掉，需要重构
        }

        // TODO: 重构这部分数据处理逻辑
        // 暂时注释掉所有experiment相关的代码
    }

    // TODO: 重构数据初始化逻辑以使用新的TicChartData结构
    // 暂时注释掉旧的ExperimentMap和TicData处理逻辑
    qDebug() << "LxDataReader: 数据初始化逻辑需要重构，暂时跳过";
    // 注释掉残留的ticdata和experiment处理代码
    qDebug().noquote() << "加载完成" << numXIC << numOtherLine;
}

bool LxDataReader::loadFileMass(int index, const std::vector<qint64> &pIndexArray, QByteArray &pDataY, QByteArray &pStreamBody, QString pFilePath)
{
    // cursor: 检查文件路径是否为空，如果为空则返回false
    if (pFilePath.isEmpty())
        return false;

    int nFrameB = index;
    int nFrameE = index;
    if ((pIndexArray.size() <= nFrameB) || (pIndexArray.size() <= nFrameE))
        return false;
    if (!pFilePath.isEmpty())
    {
        QFile tmpDataFileMass(pFilePath);
        try
        {
            if (!tmpDataFileMass.open(QIODevice::ReadOnly))
                return false;
            tmpDataFileMass.seek(0);
        }
        catch (...)
        {
            return false;
        }
        int tmpACC = 0;
        QByteArray uncompressBuff;
        do
        {
            qint64 offset = pIndexArray[nFrameB + tmpACC];
            qint64 sizeBody = 0;
            qint64 sizeStreamBody = sizeof(_StreamBody);
            if (offset >= tmpDataFileMass.size())
                return false;
            pStreamBody.resize(sizeStreamBody);
            try
            {
                if (!tmpDataFileMass.seek(offset))
                {
                    tmpDataFileMass.close();
                    return false;
                }
                if (sizeStreamBody != tmpDataFileMass.read(pStreamBody.data(), sizeStreamBody))
                {
                    tmpDataFileMass.close();
                    return false;
                }
                _StreamBody *tmpStreamBody = (_StreamBody *)(pStreamBody.data());
                if (tmpStreamBody->lengthParam > 0)
                {
                    ulong lengthParam = tmpStreamBody->lengthParam;
                    pStreamBody.resize(sizeStreamBody + lengthParam);
                    tmpDataFileMass.read(pStreamBody.data() + sizeStreamBody, lengthParam);
                    tmpStreamBody = (_StreamBody *)(pStreamBody.data());
                }
                sizeBody = tmpStreamBody->length - sizeStreamBody - tmpStreamBody->lengthParam;
                QByteArray tmpArray;
                tmpArray.resize(sizeBody);
                if (sizeBody != tmpDataFileMass.read(tmpArray.data(), sizeBody))
                {
                    tmpDataFileMass.close();
                    return false;
                }
                switch (tmpStreamBody->typeParam)
                {
                case _StreamBody::Type_Uint16Compress:
                    uncompressBuff = qUncompress(tmpArray);
                    fillData<quint16>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Uint16:
                    fillData<quint16>(tmpArray, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_FloatCompress:
                    uncompressBuff = qUncompress(tmpArray);
                    fillData<float>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Float:
                    fillData<float>(tmpArray, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_DoubleCompress:
                    uncompressBuff = qUncompress(tmpArray);
                    fillData<double>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Double:
                    fillData<double>(tmpArray, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Uint8Compress:
                    uncompressBuff = qUncompress(tmpArray);
                    fillData<quint8>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Uint8:
                    fillData<quint8>(tmpArray, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Uint32Compress:
                    uncompressBuff = qUncompress(tmpArray);
                    fillData<quint32>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Uint32:
                    fillData<quint32>(tmpArray, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                default:
                    break;
                }
            }
            catch (...)
            {
                return false;
            }
        } while (++tmpACC + nFrameB <= nFrameE);
        return true;
    }
    return false;
}

void LxDataReader::qDebugStreamHead(_StreamHead *streamHead)
{
    qDebug().noquote() << "头长度:" << streamHead->length;
    QDateTime dt = QDateTime::fromSecsSinceEpoch(streamHead->dateTime, Qt::UTC);
    qDebug().noquote() << "本地时间：" << streamHead->dateTime << dt.toLocalTime().toString("yyyy-MM-dd hh:mm:ss");
    QString baseType;
    switch (streamHead->typeParam)
    {
    case _StreamHead::Type_Param::Type_Null:
    {
        baseType = "Type_Null";
        break;
    };
    case _StreamHead::Type_Param::Type_Tuning:
    {
        baseType = "Type_Tuning";
        break;
    };
    case _StreamHead::Type_Param::Type_Acquisition:
    {
        baseType = "Type_Acquisition";
        break;
    };
    case _StreamHead::Type_Param::Type_Tuning_RGA:
    {
        baseType = "Type_Tuning_RGA";
        break;
    };
    case _StreamHead::Type_Param::Type_Moniter_RGA:
    {
        baseType = "Type_Moniter_RGA";
        break;
    };
    case _StreamHead::Type_Param::Type_VacuumDiagnostics_RGA:
    {
        baseType = "Type_VacuumDiagnostics_RGA";
        break;
    };
    case _StreamHead::Type_Param::Type_LeakCheck_RGA:
    {
        baseType = "Type_LeakCheck_RGA";
        break;
    }
    }
    qDebug().noquote() << "基础类型:" + baseType;
}

void LxDataReader::qDebugStreamHeadParam(cParamValue::_StreamHeadParam *param)
{
    qDebug().noquote() << "   cParamValue::_StreamHeadParam长度:" << param->length;
    QString _StreamHeadParamType;
    switch (param->type)
    {
    case cParamValue::Type_Child_Param::Type_Child_Null:
    {
        _StreamHeadParamType = "Type_Child_Null";
        break;
    }
    case cParamValue::Type_Child_Param::Type_Segment_Param /*二进制参数编码*/:
    {
        _StreamHeadParamType = "Type_Segment_Param";
        // 读取Segment
        QByteArray tmpArray;
        int dataSize = param->length - sizeof(cParamValue::_StreamHeadParam);
        tmpArray.resize(dataSize);

        // 从param中的数据部分开始复制
        char *dataStart = reinterpret_cast<char *>(param) + sizeof(cParamValue::_StreamHeadParam);
        memcpy(tmpArray.data(), dataStart, dataSize);

        // 如果您确实需要指针，请确保QByteArray在整个使用期间保持有效
        cParamValue::_Segment *tmpSegment = reinterpret_cast<cParamValue::_Segment *>(tmpArray.data());
        QString segType;
        // 使用tmpSegment
        switch (tmpSegment->type)
        {
        case cParamValue::Type_Segment::Type_Seg_Null:
        {
            segType = "Type_Seg_Null";
        }
        case cParamValue::Type_Segment::Type_LIT_TARGET:
        {
            segType = "Type_LIT_TARGET";
        }
        case cParamValue::Type_Segment::Type_LIT_2019:
        {
            segType = "Type_LIT_2019";
        }
        case cParamValue::Type_Segment::Type_LIT_FULL:
        {
            segType = "Type_LIT_FULL";
        }
        case cParamValue::Type_Segment::Type_Seg_RGA:
        {
            segType = "Type_Seg_RGA";
        }
        case cParamValue::Type_Segment::Type_Seg_TripleQ:
        {
            segType = "Type_Seg_TripleQ";
        }
        }
        qDebug().noquote() << "         Segment Type:" + segType;

        qDebug() << "segment length:" << tmpSegment->length;
        qDebug() << "segment countsEvent:" << tmpSegment->countsEvent;
        qDebug() << "segment lengthEvent:" << tmpSegment->lengthEvent;
        qDebug() << "segment lengthReserved:" << tmpSegment->lengthReserved;
        qDebug() << "segment time:" << tmpSegment->getSegTimeMs(tmpSegment);
        // ...
        qDebugEvents(tmpSegment);
        break;
    }
    case cParamValue::Type_Child_Param::Type_Method_Param /*字符串参数编码*/:
    {
        _StreamHeadParamType = "Type_Method_Param";
        break;
    }
    case cParamValue::Type_Child_Param::Type_Process_Param /*二进制处理方法编码暂未启用*/:
    {
        _StreamHeadParamType = "Type_Process_Param";
        break;
    }
    case cParamValue::Type_Child_Param::Type_XIC_Param /*字符串XIC方法编码*/:
    {
        _StreamHeadParamType = "Type_XIC_Param";
        break;
    }
    case cParamValue::Type_Child_Param::Type_Tuning_Param /*调谐文件内容*/:
    {
        _StreamHeadParamType = "Type_Tuning_Param";
        break;
    }
    case cParamValue::Type_Child_Param::Type_XIC_Select /*字符串XIC方法编码*/:
    {
        _StreamHeadParamType = "Type_XIC_Select";
        break;
    }
    case cParamValue::Type_Child_Param::Type_Property_Str /*字符串属性property1&value1;property2&value2*/:
    {
        _StreamHeadParamType = "Type_Property_Str";
        break;
    }
    case cParamValue::Type_Child_Param::Type_Line_Param /*除了TIC、XIC外的绘图数据绘图数据长度==TIC*/:
    {
        _StreamHeadParamType = "Type_Line_Param";
        break;
    }
    }

    qDebug().noquote() << "   -------_StreamHeadParam类型:" + _StreamHeadParamType;
}
template <typename T>
void LxDataReader::fillData(const QByteArray &pSrcData, QByteArray &pDstData, int countACC, int nFrameB, int nFrameE)
{
    qint64 sizeData = pSrcData.size() / sizeof(T);
    const T *pSrcY = reinterpret_cast<const T *>(pSrcData.data());
    if (countACC == 0)
    {
        pDstData.resize(sizeData * sizeof(double));
        double *pDstY = (double *)pDstData.data();
        for (int uIndex = 0; uIndex < sizeData; ++uIndex)
        {
            pDstY[uIndex] = (double)(pSrcY[uIndex]);
        }
    }
    else if (countACC + nFrameB == nFrameE)
    {
        double *pDstY = (double *)pDstData.data();
        for (int uIndex = 0; uIndex < sizeData; ++uIndex)
        {
            pDstY[uIndex] = (double)(countACC - 1) / (double)(countACC)*pDstY[uIndex] + (double)(pSrcY[uIndex]) / (double)(countACC);
            pDstY[uIndex] = pDstY[uIndex]; // pDstY[uIndex]= pDstY[uIndex]* dbRawScale- 4.197;
        }
    }
    else
    {
        double *pDstY = (double *)pDstData.data();
        for (int uIndex = 0; uIndex < sizeData; ++uIndex)
        {
            pDstY[uIndex] = (double)(countACC - 1) / (double)(countACC)*pDstY[uIndex] + (double)(pSrcY[uIndex]) / (double)(countACC);
        }
    }
}
void LxDataReader::qDebugEvents(cParamValue::_Segment *pSegment)
{
    int offsetP = 0;

    for (uint32_t currentEvt = 0; currentEvt < pSegment->countsEvent; ++currentEvt)
    {
        cParamValue::_Event *pEvent = (cParamValue::_Event *)((char *)&(pSegment->fisrtEvent) + offsetP + pSegment->lengthReserved);
        QString eventType;
        qDebug() << "holdTimeMs:" << pEvent->holdTimeMs;
        qDebug() << "delayTimeMs:" << pEvent->delayTimeMs;
        qDebug() << "preReadyTimeMs:" << pEvent->preReadyTimeMs;
        qDebug() << "postReadyTimeMs:" << pEvent->postReadyTimeMs;
        qDebug() << "minTimeMs:" << pEvent->minTimeMs;
        qDebug() << "msPrecursor:" << pEvent->msPrecursor;
        qDebug() << "title:" << pEvent->title;
        qDebug() << "lengthReserved:" << pEvent->lengthReserved;
        if (cParamValue::Type_SIM == pEvent->type)
        {
            eventType = "_EventSIM";
            qDebug() << "eventType:" << eventType;
            cParamValue::_EventSIM *eventSIM = static_cast<cParamValue::_EventSIM *>(pEvent);
            qDebug() << "eventSIM length:" << eventSIM->length();
            qDebug() << "eventSIM size:" << eventSIM->size();

            for (uint i = 0; i < CHANNEL_COUNTS_MAX; i++)
            {
                qDebug() << eventSIM->mass[i] << "," << eventSIM->massOrig[i] << "," << eventSIM->timeMs[i];
            }

            offsetP += sizeof(cParamValue::_EventSIM);
        }
        else if (cParamValue::Type_Scan_RGA == pEvent->type)
        {
            eventType = "_EventScanRGA";
            qDebug() << "eventType:" << eventType;
            offsetP += sizeof(cParamValue::_EventScanRGA);

            cParamValue::_EventScanRGA *eventScanRGA = static_cast<cParamValue::_EventScanRGA *>(pEvent);

            qDebug() << "dwell_time:" << eventScanRGA->dwell_time;
            qDebug() << "points_per_amu:" << eventScanRGA->points_per_amu;
            qDebug() << "mass_begin:" << eventScanRGA->mass_begin;
            qDebug() << "mass_end:" << eventScanRGA->mass_end;
        }
        else
        {
            eventType = "_EventLIT";
            qDebug() << "eventType:" << eventType;
            offsetP += sizeof(cParamValue::_EventLIT);
            cParamValue::_EventLIT *eventLIT = static_cast<cParamValue::_EventLIT *>(pEvent);

            qDebug() << eventLIT->msStart << "," << eventLIT->msEnd << "," << eventLIT->msStartOrig << "," << eventLIT->msEndOrig;
        }
    }
}

int LxDataReader::getNumOtherLine(const QString &str, QList<std::vector<double>> &pStructLines)
{
    pStructLines.clear();
    if (str.isEmpty())
        return 0;
    int counts = 0;
    QStringList lstStr = str.split(';');
    foreach (auto tmpStr, lstStr)
    {
        if (tmpStr.isEmpty())
            continue;
        QStringList tmpList = tmpStr.split('&');
        if (tmpList.size() < 2)
            continue;
        int tmpLines = tmpList[1].toInt();
        if (tmpLines < 1)
            continue;
        counts += tmpLines;
        //        QList<std::vector<double>> structList;
        //        for(int i=0; i<tmpLines; ++i){
        //            structList<< std::vector<double>(0);
        //        }
        pStructLines << std::vector<double>(0);
    }
    return counts;
}

QString LxDataReader::getNumOtherLine(const QByteArray &pStreamHead)
{
    QString LineString;
    QList<cParamValue::_StreamHeadParam *> tmpList;
    if (!_StreamHead::toList(pStreamHead, tmpList))
        return QString();
    for (int i = 0; i < tmpList.size(); ++i)
    {
        switch (tmpList[i]->type)
        {
        case cParamValue::Type_Line_Param:
        {
            QByteArray tmpArray;
            tmpArray.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            LineString = QString::fromUtf8(tmpArray);
            //            QStringList lstStr = XicString.split('/');
            //            if(lstStr.size()< 2)
            //                return QString();
            //            if(lstStr[1].isEmpty())
            //                return QString();
            return LineString;
        }
        default:
            break;
        }
    }
    return QString();
}

int LxDataReader::updateXIC(const QString &XicString)
{
    QStringList lstStr = XicString.split('/');
    if (lstStr.size() < 2)
        return 0;
    QStringList lstCurves = lstStr[1].split('@');
    return lstCurves.size();
}
QString LxDataReader::getNumXIC(const QByteArray &pStreamHead)
{
    QString XicString;
    QList<cParamValue::_StreamHeadParam *> tmpList;
    if (!_StreamHead::toList(pStreamHead, tmpList))
        return QString();
    for (int i = 0; i < tmpList.size(); ++i)
    {
        switch (tmpList[i]->type)
        {
        case cParamValue::Type_XIC_Param:
        {
            QByteArray tmpArray;
            tmpArray.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            XicString = QString::fromUtf8(tmpArray);
            QStringList lstStr = XicString.split('/');
            if (lstStr.size() < 2)
                return QString();
            if (lstStr[1].isEmpty())
                return QString();
            return XicString;
        }
        default:
            break;
        }
    }
    return QString();
}

// ========== 从LxCustomDataReader迁移的方法实现 ==========

bool LxDataReader::loadTICDataComplete(FileData *data)
{
    qDebug() << "LxDataReader::loadTICDataComplete: 开始加载完整TIC数据";

    if (!data)
    {
        qDebug() << "LxDataReader::loadTICDataComplete: FileData指针为空";
        return false;
    }

    QString filePath = data->getFilePath();
    if (filePath.isEmpty())
    {
        qDebug() << "LxDataReader::loadTICDataComplete: 文件路径为空";
        return false;
    }

    // 读取文件头
    QByteArray streamHead;
    if (!readFileHeader(filePath, streamHead))
    {
        qDebug() << "LxDataReader::loadTICDataComplete: 读取文件头失败";
        return false;
    }

    // 解析StreamHead中的Segment信息
    if (!parseSegmentFromStreamHead(streamHead, data))
    {
        qDebug() << "LxDataReader::loadTICDataComplete: 解析Segment信息失败";
        return false;
    }

    // 解析StreamHead
    int numXIC = 0, numOtherLine = 0;
    QMap<quint32, QMap<QString, XICParam *>> xicMap;
    if (!parseStreamHead(streamHead, numXIC, numOtherLine, xicMap))
    {
        qDebug() << "LxDataReader::loadTICDataComplete: 解析StreamHead失败";
        return false;
    }

    // 读取所有TIC数据
    std::vector<qint64> indexArray;
    std::vector<double> ticX, ticY;
    QList<std::vector<double>> structLines;

    if (!readAllTICData(filePath, streamHead, numXIC, numOtherLine,
                        indexArray, ticX, ticY, xicMap, structLines))
    {
        qDebug() << "LxDataReader::loadTICDataComplete: 读取TIC数据失败";
        return false;
    }

    // 设置数据到FileData
    data->indexArray = indexArray;
    data->dataTIC_X = ticX;
    data->dataTIC_Y = ticY;
    data->otherLinesY = structLines;
    data->streamHead = streamHead;
    // data->xicMap = xicMap; // 类型不匹配，暂时注释掉，XIC数据已经通过TicChartData管理

    // 关键修复：设置timePoints和frameIndices数组
    data->timePoints.clear();
    data->frameIndices.clear();
    data->timePoints.reserve(ticX.size());
    data->frameIndices.reserve(indexArray.size());

    for (size_t i = 0; i < ticX.size(); ++i)
    {
        data->timePoints.append(ticX[i]);
    }

    for (size_t i = 0; i < indexArray.size(); ++i)
    {
        data->frameIndices.append(indexArray[i]);
    }

    qDebug() << "LxDataReader::loadTICDataComplete: 设置timePoints数组大小:" << data->timePoints.size();
    qDebug() << "LxDataReader::loadTICDataComplete: 设置frameIndices数组大小:" << data->frameIndices.size();

    // 创建TicChartData对象并添加到TicMap中
    int eventId = 0; // 默认事件ID为0
    TicChartData *ticData = data->getTicData(eventId);
    if (!ticData)
    {
        ticData = data->createTicData(eventId);
        if (ticData)
        {
            qDebug() << "LxDataReader::loadTICDataComplete: 创建TicChartData成功，事件ID:" << eventId;
        }
        else
        {
            qDebug() << "LxDataReader::loadTICDataComplete: 创建TicChartData失败，事件ID:" << eventId;
            return false;
        }
    }

    // 将TIC数据设置到TicChartData中
    if (ticData && !ticX.empty() && !ticY.empty())
    {
        QVector<QPointF> ticPoints;
        ticPoints.reserve(ticX.size());
        for (size_t i = 0; i < ticX.size() && i < ticY.size(); ++i)
        {
            ticPoints.append(QPointF(ticX[i], ticY[i]));
        }
        ticData->setData(ticPoints);
        qDebug() << "LxDataReader::loadTICDataComplete: TIC数据设置到TicChartData完成，数据点数:" << ticPoints.size();
    }

    qDebug() << "LxDataReader::loadTICDataComplete: TIC数据加载完成，数据点数:" << ticX.size();
    return true;
}

bool LxDataReader::readFileHeader(const QString &filePath, QByteArray &streamHead)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        qDebug() << "LxDataReader::readFileHeader: 无法打开文件:" << filePath;
        return false;
    }

    // 先读取基本头部信息
    qint64 sizeHead = sizeof(_StreamHead);
    streamHead.resize(sizeHead);

    if (sizeHead != file.read(streamHead.data(), sizeHead))
    {
        qDebug() << "LxDataReader::readFileHeader: 读取基本头部失败";
        file.close();
        return false;
    }

    // 获取完整头部长度
    _StreamHead *tmpStreamHead = (_StreamHead *)(streamHead.data());
    sizeHead = tmpStreamHead->length;
    streamHead.resize(sizeHead);

    // 重新读取完整头部
    file.seek(0);
    if (sizeHead != file.read(streamHead.data(), sizeHead))
    {
        qDebug() << "LxDataReader::readFileHeader: 读取完整头部失败";
        file.close();
        return false;
    }

    file.close();
    qDebug() << "LxDataReader::readFileHeader: 文件头读取成功，大小:" << sizeHead;
    return true;
}

bool LxDataReader::parseSegmentFromStreamHead(const QByteArray &streamHead, FileData *data)
{
    QList<cParamValue::_StreamHeadParam *> tmpList;
    if (!_StreamHead::toList(streamHead, tmpList))
    {
        qDebug() << "LxDataReader::parseSegmentFromStreamHead: 解析StreamHead失败";
        return false;
    }

    for (int i = 0; i < tmpList.size(); ++i)
    {
        if (tmpList[i]->type == cParamValue::Type_Segment_Param)
        {
            // 提取Segment数据
            QByteArray segmentData;
            int dataSize = tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam);
            segmentData.resize(dataSize);

            char *dataStart = reinterpret_cast<char *>(tmpList[i]) + sizeof(cParamValue::_StreamHeadParam);
            memcpy(segmentData.data(), dataStart, dataSize);

            // 添加到FileData的mSegment列表
            data->mSegment.append(segmentData);
            qDebug() << "LxDataReader::parseSegmentFromStreamHead: 解析到Segment数据，大小:" << dataSize;
        }
    }

    return !data->mSegment.isEmpty();
}

bool LxDataReader::parseStreamHead(const QByteArray &streamHead,
                                   int &numXIC, int &numOtherLine,
                                   QMap<quint32, QMap<QString, XICParam *>> &xicMap)
{
    // 提取XIC字符串
    QString xicString = extractXICString(streamHead);
    numXIC = parseXICString(xicString, xicMap);

    // 提取OtherLine字符串
    QString otherLineString = extractOtherLineString(streamHead);
    QList<std::vector<double>> structLines;
    numOtherLine = parseOtherLineString(otherLineString, structLines);

    qDebug() << "LxDataReader::parseStreamHead: XIC数量:" << numXIC << ", OtherLine数量:" << numOtherLine;
    return true;
}

QString LxDataReader::extractXICString(const QByteArray &streamHead)
{
    QList<cParamValue::_StreamHeadParam *> tmpList;
    if (!_StreamHead::toList(streamHead, tmpList))
        return QString();

    for (int i = 0; i < tmpList.size(); ++i)
    {
        if (tmpList[i]->type == cParamValue::Type_XIC_Param)
        {
            QByteArray tmpArray;
            tmpArray.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            return QString::fromUtf8(tmpArray);
        }
    }
    return QString();
}

QString LxDataReader::extractOtherLineString(const QByteArray &streamHead)
{
    QList<cParamValue::_StreamHeadParam *> tmpList;
    if (!_StreamHead::toList(streamHead, tmpList))
        return QString();

    for (int i = 0; i < tmpList.size(); ++i)
    {
        if (tmpList[i]->type == cParamValue::Type_Line_Param)
        {
            QByteArray tmpArray;
            tmpArray.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            return QString::fromUtf8(tmpArray);
        }
    }
    return QString();
}

int LxDataReader::parseXICString(const QString &xicString,
                                 QMap<quint32, QMap<QString, XICParam *>> &xicMap)
{
    if (xicString.isEmpty())
    {
        return 0;
    }

    QStringList parts = xicString.split('/');
    if (parts.size() < 2)
    {
        return 0;
    }

    QStringList xicList = parts[1].split('@');
    int count = 0;

    for (const QString &xicItem : xicList)
    {
        if (xicItem.isEmpty())
            continue;

        // 解析XIC参数（这里简化处理，实际可能需要更复杂的解析）
        QStringList xicParams = xicItem.split('&');
        if (xicParams.size() >= 2)
        {
            double mz = xicParams[0].toDouble();
            double tolerance = xicParams[1].toDouble();
            QString name = xicParams.size() > 2 ? xicParams[2] : QString("XIC_%1").arg(count);
            int eventId = xicParams.size() > 3 ? xicParams[3].toInt() : 0;

            XICParam *param = new XICParam(mz, tolerance, name, eventId);
            xicMap[eventId][name] = param;
            count++;
        }
    }

    return count;
}

int LxDataReader::parseOtherLineString(const QString &lineString,
                                       QList<std::vector<double>> &structLines)
{
    structLines.clear();
    if (lineString.isEmpty())
        return 0;

    int counts = 0;
    QStringList lstStr = lineString.split(';');
    foreach (auto tmpStr, lstStr)
    {
        if (tmpStr.isEmpty())
            continue;
        QStringList tmpList = tmpStr.split('&');
        if (tmpList.size() < 2)
            continue;
        int tmpLines = tmpList[1].toInt();
        if (tmpLines < 1)
            continue;
        counts += tmpLines;
        structLines << std::vector<double>(0);
    }
    return counts;
}

bool LxDataReader::readAllTICData(const QString &filePath, const QByteArray &streamHead,
                                  int numXIC, int numOtherLine,
                                  std::vector<qint64> &indexArray,
                                  std::vector<double> &ticX,
                                  std::vector<double> &ticY,
                                  QMap<quint32, QMap<QString, XICParam *>> &xicMap,
                                  QList<std::vector<double>> &structLines)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        qDebug() << "LxDataReader: 无法打开文件进行数据读取" << filePath;
        return false;
    }

    qint64 headerSize = streamHead.size();
    qint64 dataSize = file.size() - headerSize;

    if (dataSize <= 0)
    {
        qDebug() << "LxDataReader: 文件中没有数据";
        indexArray.clear();
        ticX.clear();
        ticY.clear();
        file.close();
        return true;
    }

    // 计算每行数据的大小
    int sizeI64 = sizeof(qint64);
    int sizeD = sizeof(double);
    int offsetI = sizeI64 + sizeD * 2 + sizeD * numXIC + sizeD * numOtherLine;

    qint64 lineCount = dataSize / offsetI;

    if (lineCount <= 0)
    {
        qDebug() << "LxDataReader: 计算的行数无效";
        file.close();
        return false;
    }

    // 读取所有数据（关键：这里不使用Period限制）
    QByteArray allData;
    allData.resize(dataSize);

    file.seek(headerSize);
    if (file.read(allData.data(), dataSize) != dataSize)
    {
        qDebug() << "LxDataReader: 读取数据失败";
        file.close();
        return false;
    }
    file.close();

    // 预分配内存
    indexArray.resize(lineCount);
    ticX.resize(lineCount);
    ticY.resize(lineCount);

    // 初始化其他数据线
    structLines.clear();
    for (int j = 0; j < numOtherLine; ++j)
    {
        structLines.append(std::vector<double>(lineCount));
    }

    // 初始化XIC数据
    for (auto &eventMap : xicMap)
    {
        for (auto *xicParam : eventMap)
        {
            if (xicParam)
            {
                xicParam->yListXIC.resize(lineCount);
            }
        }
    }

    // 解析数据
    int offsetY = sizeI64 + sizeD;
    int offsetXIC = sizeI64 + sizeD + sizeD;
    int offsetOtherLine = offsetXIC + numXIC * sizeD;

    for (qint64 i = 0; i < lineCount; ++i)
    {
        int lineOffset = i * offsetI;

        // 读取索引、时间和TIC强度
        memcpy(&indexArray[i], allData.data() + lineOffset, sizeI64);
        memcpy(&ticX[i], allData.data() + lineOffset + sizeI64, sizeD);
        memcpy(&ticY[i], allData.data() + lineOffset + offsetY, sizeD);

        // 读取XIC数据
        if (!xicMap.isEmpty())
        {
            int xicIndex = 0;
            QMutableMapIterator<quint32, QMap<QString, XICParam *>> eventIter(xicMap);
            while (eventIter.hasNext())
            {
                QMutableMapIterator<QString, XICParam *> massIter(eventIter.next().value());
                while (massIter.hasNext())
                {
                    XICParam *xicParam = massIter.next().value();
                    if (xicParam && xicIndex < numXIC)
                    {
                        memcpy(&xicParam->yListXIC[i],
                               allData.data() + lineOffset + offsetXIC + sizeD * xicIndex, sizeD);
                        ++xicIndex;
                    }
                }
            }
        }

        // 读取其他数据线
        for (int j = 0; j < numOtherLine; ++j)
        {
            memcpy(&structLines[j][i],
                   allData.data() + lineOffset + offsetOtherLine + sizeD * j, sizeD);
        }
    }

    qDebug() << "LxDataReader: 成功读取所有数据，共" << lineCount << "行";
    return true;
}

// ========== 从MRMReader迁移的方法实现 ==========

GlobalEnums::ScanMode LxDataReader::getCurrentScanMode(FileData &data)
{
    // 检查FileData是否有Segment信息
    if (data.mSegment.empty())
    {
        qDebug() << "LxDataReader::getCurrentScanMode: 没有Segment信息";
        return GlobalEnums::ScanMode::FullScan; // 默认返回全扫描模式
    }

    // 解析第一个Segment的QByteArray数据
    const QByteArray &segmentData = data.mSegment[0];
    if (segmentData.size() < sizeof(cParamValue::_Segment))
    {
        qDebug() << "LxDataReader::getCurrentScanMode: Segment数据不完整";
        qDebug() << "   数据大小:" << segmentData.size() << ", 需要:" << sizeof(cParamValue::_Segment);
        return GlobalEnums::ScanMode::FullScan;
    }

    // 将QByteArray转换为_Segment结构体
    const cParamValue::_Segment *pSegment = reinterpret_cast<const cParamValue::_Segment *>(segmentData.constData());

    if (pSegment->countsEvent == 0)
    {
        qDebug() << "LxDataReader::getCurrentScanMode: Segment中没有事件";
        return GlobalEnums::ScanMode::FullScan;
    }

    // 获取第一个事件的指针
    const cParamValue::_Event *pEvent = reinterpret_cast<const cParamValue::_Event *>(&(pSegment->fisrtEvent));

    // 根据事件类型判断扫描模式
    // 映射关系基于 cPublicCCS.h 中的 Type_Event 枚举
    switch (pEvent->type)
    {
    // SIM扫描模式
    case cParamValue::Type_SIM:
    case cParamValue::Type_SIM_2048:
    case cParamValue::Type_SIM_RGA:
        qDebug() << "LxDataReader::getCurrentScanMode: 检测到SIM扫描模式，事件类型:" << pEvent->type;
        return GlobalEnums::ScanMode::SIM;

    // MRM扫描模式
    case cParamValue::Type_MRM:
    case cParamValue::Type_MRM_2048:
        qDebug() << "LxDataReader::getCurrentScanMode: 检测到MRM扫描模式，事件类型:" << pEvent->type;
        return GlobalEnums::ScanMode::MRM;

    // 全扫描模式
    case cParamValue::Type_Scan:
    case cParamValue::Type_Scan_RGA:
    case cParamValue::Type_Scan_RCT:
    case cParamValue::Type_Profile:
    case cParamValue::Type_LIT:
    case cParamValue::Type_LIT2019:
        qDebug() << "LxDataReader::getCurrentScanMode: 检测到全扫描模式，事件类型:" << pEvent->type;
        return GlobalEnums::ScanMode::FullScan;

    // 其他特殊类型（暂时归类为全扫描）
    case cParamValue::Type_Static_RCT:
    case cParamValue::Type_MassBar:
    case cParamValue::Type_VacuumDiagnostics_RGA:
    case cParamValue::Type_LeakCheck_RGA:
    case cParamValue::Type_Event_Null:
        qDebug() << "LxDataReader::getCurrentScanMode: 检测到特殊事件类型:" << pEvent->type << "，归类为全扫描模式";
        return GlobalEnums::ScanMode::FullScan;

    default:
        qDebug() << "LxDataReader::getCurrentScanMode: 未知事件类型:" << pEvent->type << ", 默认为全扫描模式";
        return GlobalEnums::ScanMode::FullScan;
    }
}

GlobalEnums::ScanMode LxDataReader::getScanModeForEvent(FileData &data, int eventId)
{
    // 检查FileData是否有Segment信息
    if (data.mSegment.empty())
    {
        qDebug() << "LxDataReader::getScanModeForEvent: 没有Segment信息，eventId:" << eventId;
        return GlobalEnums::ScanMode::FullScan; // 默认返回全扫描模式
    }

    // 解析第一个Segment的QByteArray数据
    const QByteArray &segmentData = data.mSegment[0];
    if (segmentData.size() < sizeof(cParamValue::_Segment))
    {
        qDebug() << "LxDataReader::getScanModeForEvent: Segment数据不完整，eventId:" << eventId;
        qDebug() << "   数据大小:" << segmentData.size() << ", 需要:" << sizeof(cParamValue::_Segment);
        return GlobalEnums::ScanMode::FullScan;
    }

    // 将QByteArray转换为_Segment结构体
    const cParamValue::_Segment *pSegment = reinterpret_cast<const cParamValue::_Segment *>(segmentData.constData());

    if (pSegment->countsEvent == 0)
    {
        qDebug() << "LxDataReader::getScanModeForEvent: Segment中没有事件，eventId:" << eventId;
        return GlobalEnums::ScanMode::FullScan;
    }

    // 检查eventId是否有效
    if (eventId < 0 || eventId >= static_cast<int>(pSegment->countsEvent))
    {
        qDebug() << "LxDataReader::getScanModeForEvent: 无效的eventId:" << eventId
                 << ", 总事件数:" << pSegment->countsEvent;
        return GlobalEnums::ScanMode::FullScan;
    }

    // 遍历到指定的事件
    int offsetP = 0;
    const cParamValue::_Event *pEvent = nullptr;

    for (uint32_t currentEvt = 0; currentEvt <= static_cast<uint32_t>(eventId); ++currentEvt)
    {
        pEvent = reinterpret_cast<const cParamValue::_Event *>((char *)&(pSegment->fisrtEvent) + offsetP);

        if (currentEvt == static_cast<uint32_t>(eventId))
        {
            break; // 找到目标事件
        }

        // 根据事件类型更新偏移量
        switch (pEvent->type)
        {
        case cParamValue::Type_SIM:
        case cParamValue::Type_SIM_2048:
            offsetP += sizeof(cParamValue::_EventSIM);
            break;
        case cParamValue::Type_MRM:
        case cParamValue::Type_MRM_2048:
            offsetP += sizeof(cParamValue::_EventSIM); // MRM使用与SIM相同的结构
            break;
        case cParamValue::Type_Scan_RGA:
            offsetP += sizeof(cParamValue::_EventScanRGA);
            break;
        default:
            offsetP += sizeof(cParamValue::_EventLIT);
            break;
        }
    }

    if (!pEvent)
    {
        qDebug() << "LxDataReader::getScanModeForEvent: 无法找到事件，eventId:" << eventId;
        return GlobalEnums::ScanMode::FullScan;
    }

    // 根据事件类型判断扫描模式
    // 映射关系基于 cPublicCCS.h 中的 Type_Event 枚举
    switch (pEvent->type)
    {
    // SIM扫描模式
    case cParamValue::Type_SIM:
    case cParamValue::Type_SIM_2048:
    case cParamValue::Type_SIM_RGA:
        qDebug() << "LxDataReader::getScanModeForEvent: Event" << eventId << "检测到SIM扫描模式，事件类型:" << pEvent->type;
        return GlobalEnums::ScanMode::SIM;

    // MRM扫描模式
    case cParamValue::Type_MRM:
    case cParamValue::Type_MRM_2048:
        qDebug() << "LxDataReader::getScanModeForEvent: Event" << eventId << "检测到MRM扫描模式，事件类型:" << pEvent->type;
        return GlobalEnums::ScanMode::MRM;

    // 全扫描模式
    case cParamValue::Type_Scan:
    case cParamValue::Type_Scan_RGA:
    case cParamValue::Type_Scan_RCT:
    case cParamValue::Type_Profile:
    case cParamValue::Type_LIT:
    case cParamValue::Type_LIT2019:
        qDebug() << "LxDataReader::getScanModeForEvent: Event" << eventId << "检测到全扫描模式，事件类型:" << pEvent->type;
        return GlobalEnums::ScanMode::FullScan;

    // 其他特殊类型（暂时归类为全扫描）
    case cParamValue::Type_Static_RCT:
    case cParamValue::Type_MassBar:
    case cParamValue::Type_VacuumDiagnostics_RGA:
    case cParamValue::Type_LeakCheck_RGA:
    case cParamValue::Type_Event_Null:
        qDebug() << "LxDataReader::getScanModeForEvent: Event" << eventId << "检测到特殊事件类型:" << pEvent->type << "，归类为全扫描模式";
        return GlobalEnums::ScanMode::FullScan;

    default:
        qDebug() << "LxDataReader::getScanModeForEvent: Event" << eventId << "未知事件类型:" << pEvent->type << ", 默认为全扫描模式";
        return GlobalEnums::ScanMode::FullScan;
    }
}

int LxDataReader::getEventCount(FileData &data)
{
    // 检查FileData是否有Segment信息
    if (data.mSegment.empty())
    {
        qDebug() << "LxDataReader::getEventCount: 没有Segment信息";
        return 0;
    }

    // 解析第一个Segment的QByteArray数据
    const QByteArray &segmentData = data.mSegment[0];
    if (segmentData.size() < sizeof(cParamValue::_Segment))
    {
        qDebug() << "LxDataReader::getEventCount: Segment数据不完整";
        qDebug() << "   数据大小:" << segmentData.size() << ", 需要:" << sizeof(cParamValue::_Segment);
        return 0;
    }

    // 将QByteArray转换为_Segment结构体
    const cParamValue::_Segment *pSegment = reinterpret_cast<const cParamValue::_Segment *>(segmentData.constData());

    qDebug() << "LxDataReader::getEventCount: Segment中有" << pSegment->countsEvent << "个事件";
    return static_cast<int>(pSegment->countsEvent);
}

bool LxDataReader::isMRMData(FileData &data)
{
    // 调用获取扫描类型的函数，判断是否为MRM类型
    return getCurrentScanMode(data) == GlobalEnums::ScanMode::MRM;
}

QVector<StructMRM> LxDataReader::getMRMDataList(FileData &data)
{
    // 清空现有的MRM数据列表
    Struct_MRM_Vec.clear();

    // 检查是否为MRM数据
    if (!isMRMData(data))
    {
        qDebug() << "当前数据不是MRM数据，无法获取MRM列表";
        return Struct_MRM_Vec;
    }

    // 检查FileData是否有Segment信息
    if (data.mSegment.empty())
    {
        qDebug() << "LxDataReader::getMRMDataList: 没有Segment信息";
        return Struct_MRM_Vec;
    }

    // 解析第一个Segment的QByteArray数据
    const QByteArray &segmentData = data.mSegment[0];
    if (segmentData.size() < sizeof(cParamValue::_Segment))
    {
        qDebug() << "LxDataReader::getMRMDataList: Segment数据不完整";
        return Struct_MRM_Vec;
    }

    // 将QByteArray转换为_Segment结构体
    const cParamValue::_Segment *pSegment = reinterpret_cast<const cParamValue::_Segment *>(segmentData.constData());

    if (pSegment->countsEvent == 0)
    {
        qDebug() << "LxDataReader::getMRMDataList: Segment中没有事件";
        return Struct_MRM_Vec;
    }

    // 处理每个事件，提取MRM数据
    int offsetP = 0;
    for (uint32_t currentEvt = 0; currentEvt < pSegment->countsEvent; ++currentEvt)
    {
        const cParamValue::_Event *pEvent = reinterpret_cast<const cParamValue::_Event *>((char *)&(pSegment->fisrtEvent) + offsetP);

        // 根据事件类型处理
        if (pEvent->type == cParamValue::Type_MRM || pEvent->type == cParamValue::Type_MRM_2048)
        {
            // 对于真正的MRM事件，提取MRM数据
            StructMRM mrmData;
            mrmData.Experiment = currentEvt + 1;               // 从1开始的实验编号
            mrmData.ID = QString("MRM%1").arg(currentEvt + 1); // 默认ID
            mrmData.Q1 = pEvent->msPrecursor;                  // 前体离子质荷比
            mrmData.Q3 = 0.0;                                  // 子离子质荷比（需要从具体事件结构中获取）
            mrmData.RT = pEvent->holdTimeMs / 1000.0;          // 保留时间(秒)

            // 添加到列表
            Struct_MRM_Vec.append(mrmData);
            qDebug() << "LxDataReader::getMRMDataList: 添加MRM数据，Q1:" << mrmData.Q1 << ", RT:" << mrmData.RT;

            // 更新偏移量（假设MRM事件结构与SIM类似）
            offsetP += sizeof(cParamValue::_EventSIM);
        }
        else if (pEvent->type == cParamValue::Type_SIM || pEvent->type == cParamValue::Type_SIM_2048)
        {
            // SIM事件，从mass数组中提取数据
            const cParamValue::_EventSIM *pEventSIM = reinterpret_cast<const cParamValue::_EventSIM *>(pEvent);

            // 创建MRM数据结构（虽然实际是SIM数据）
            StructMRM mrmData;
            mrmData.Experiment = currentEvt + 1;                              // 从1开始的实验编号
            mrmData.ID = QString("SIM%1").arg(currentEvt + 1);                // SIM类型ID
            mrmData.Q1 = pEventSIM->msPrecursor;                              // 前体离子质荷比
            mrmData.Q3 = (pEventSIM->mass[0] > 0) ? pEventSIM->mass[0] : 0.0; // 使用第一个有效质量值
            mrmData.RT = pEvent->holdTimeMs / 1000.0;                         // 保留时间(秒)

            // 添加到列表
            Struct_MRM_Vec.append(mrmData);
            qDebug() << "LxDataReader::getMRMDataList: 添加SIM数据，Q1:" << mrmData.Q1 << ", Q3:" << mrmData.Q3 << ", RT:" << mrmData.RT;

            // 更新偏移量
            offsetP += sizeof(cParamValue::_EventSIM);
        }
        else
        {
            // 其他类型事件，使用默认偏移量
            offsetP += sizeof(cParamValue::_EventLIT);
        }
    }

    qDebug() << "成功获取MRM列表，共" << Struct_MRM_Vec.size() << "条记录";
    return Struct_MRM_Vec;
}

QPair<double, double> LxDataReader::getMzRangeFromSegment(FileData *data, int eventId)
{
    qDebug() << "LxDataReader::getMzRangeFromSegment: 开始从Segment获取m/z范围";
    qDebug() << "   事件ID:" << eventId;

    // 检查FileData是否有Segment信息
    if (data->mSegment.empty())
    {
        qDebug() << "LxDataReader::getMzRangeFromSegment: 没有Segment信息";
        return qMakePair(0.0, 0.0);
    }

    // 解析第一个Segment的QByteArray数据
    const QByteArray &segmentData = data->mSegment[0];
    if (segmentData.size() < sizeof(cParamValue::_Segment))
    {
        qDebug() << "LxDataReader::getMzRangeFromSegment: Segment数据不完整";
        qDebug() << "   数据大小:" << segmentData.size() << ", 需要:" << sizeof(cParamValue::_Segment);
        return qMakePair(0.0, 0.0);
    }

    // 将QByteArray转换为_Segment结构体
    const cParamValue::_Segment *pSegment = reinterpret_cast<const cParamValue::_Segment *>(segmentData.constData());

    if (pSegment->countsEvent == 0)
    {
        qDebug() << "LxDataReader::getMzRangeFromSegment: Segment中没有事件";
        return qMakePair(0.0, 0.0);
    }

    // 获取第一个事件的指针
    cParamValue::_Event *pEvent = (cParamValue::_Event *)((char *)&(pSegment->fisrtEvent));

    // 🎯 添加更多调试信息
    qDebug() << "LxDataReader::getMzRangeFromSegment: 事件类型:" << pEvent->type;
    qDebug() << "LxDataReader::getMzRangeFromSegment: Segment事件数量:" << pSegment->countsEvent;
    qDebug() << "LxDataReader::getMzRangeFromSegment: Segment数据大小:" << segmentData.size();
    qDebug() << "LxDataReader::getMzRangeFromSegment: pEvent指针:" << (void *)pEvent;
    qDebug() << "LxDataReader::getMzRangeFromSegment: pSegment指针:" << (void *)pSegment;

    if (pEvent->type == cParamValue::Type_Scan)
    {
        // Scan事件：使用线性扫描范围
        cParamValue::_EventScan *pEventScan = (cParamValue::_EventScan *)pEvent;
        double startMass = pEventScan->msStart; // 正确的成员名称
        double endMass = pEventScan->msEnd;     // 正确的成员名称

        if (startMass > 0 && endMass > startMass)
        {
            qDebug() << "LxDataReader::getMzRangeFromSegment: Scan事件，m/z范围:" << startMass << "~" << endMass;
            return qMakePair(startMass, endMass);
        }
        else
        {
            qDebug() << "LxDataReader::getMzRangeFromSegment: Scan事件范围无效";
            qDebug() << "   起始质量:" << startMass << ", 结束质量:" << endMass;
            return qMakePair(0.0, 0.0);
        }
    }
    else if (pEvent->type == cParamValue::Type_SIM || pEvent->type == cParamValue::Type_SIM_2048)
    {
        // SIM事件：使用mass数组中的真实质量值
        cParamValue::_EventSIM *pEventSIM = (cParamValue::_EventSIM *)pEvent;

        // 从mass数组中提取有效的质量值范围
        double minValidMass = 999999.0, maxValidMass = 0.0;
        int validCount = 0;

        // 🎯 修复SIM数组越界问题：使用正确的数组大小
        // 根据文档，SIM事件的mass数组大小是64，不是2048
        // 同时使用eventSIM->length()获取实际有效长度
        int maxIndex = 64; // SIM事件mass数组的实际大小
        if (pEvent->type == cParamValue::Type_SIM_2048)
        {
            maxIndex = 2048; // Type_SIM_2048类型可能有更大的数组
        }

        // 尝试使用length()方法获取实际长度，如果可用的话
        try
        {
            int actualLength = pEventSIM->length();
            if (actualLength > 0 && actualLength < maxIndex)
            {
                maxIndex = actualLength;
            }
        }
        catch (...)
        {
            // 如果length()方法不可用，使用默认大小
        }

        qDebug() << "LxDataReader::getMzRangeFromSegment: SIM事件类型:" << pEvent->type << "，遍历范围:" << maxIndex;

        for (int i = 0; i < maxIndex; ++i)
        {
            double mass = pEventSIM->mass[i];
            // 🎯 不过滤m/z值，只检查是否为有效数值
            if (!std::isnan(mass) && !std::isinf(mass))
            {
                minValidMass = qMin(minValidMass, mass);
                maxValidMass = qMax(maxValidMass, mass);
                validCount++;
                qDebug() << "   找到质量值[" << i << "]:" << mass;
            }
        }

        if (validCount > 0)
        {
            // 🎯 修复SIM单一质量值问题：当只有一个质量值时，min和max相等，也应该返回有效范围
            if (maxValidMass == minValidMass)
            {
                // 对于单一质量值，创建一个小的范围窗口（±0.5 Da）
                double centerMass = minValidMass;
                minValidMass = centerMass - 0.5;
                maxValidMass = centerMass + 0.5;
                qDebug() << "LxDataReader::getMzRangeFromSegment: SIM事件单一质量值，扩展范围:" << minValidMass << "~" << maxValidMass;
                qDebug() << "   中心质量值:" << centerMass << "，有效质量值数量:" << validCount;
            }
            else
            {
                qDebug() << "LxDataReader::getMzRangeFromSegment: SIM事件，m/z范围:" << minValidMass << "~" << maxValidMass;
                qDebug() << "   有效质量值数量:" << validCount;
            }
            return qMakePair(minValidMass, maxValidMass);
        }
        else
        {
            qDebug() << "LxDataReader::getMzRangeFromSegment: SIM事件没有有效的质量值";
            qDebug() << "   遍历了" << maxIndex << "个元素，找到" << validCount << "个有效值";
            return qMakePair(0.0, 0.0);
        }
    }
    else if (pEvent->type == cParamValue::Type_MRM || pEvent->type == cParamValue::Type_MRM_2048)
    {
        // MRM事件：使用mass数组中的真实质量值
        double minValidMass = 999999.0, maxValidMass = 0.0;
        int validCount = 0;

        if (pEvent->type == cParamValue::Type_MRM)
        {
            cParamValue::_EventMRM *pEventMRM = (cParamValue::_EventMRM *)pEvent;

            // 🎯 添加安全检查
            if (!pEventMRM)
            {
                qDebug() << "LxDataReader::getMzRangeFromSegment: pEventMRM为空指针";
                return qMakePair(0.0, 0.0);
            }

            int mrmLength = 0;
            try
            {
                mrmLength = pEventMRM->length();
                qDebug() << "LxDataReader::getMzRangeFromSegment: MRM事件长度:" << mrmLength;
            }
            catch (...)
            {
                qDebug() << "LxDataReader::getMzRangeFromSegment: 获取MRM长度时发生异常";
                return qMakePair(0.0, 0.0);
            }

            // 检查长度的合理性
            if (mrmLength < 0 || mrmLength > 10000)
            {
                qDebug() << "LxDataReader::getMzRangeFromSegment: MRM长度异常:" << mrmLength;
                return qMakePair(0.0, 0.0);
            }

            for (int i = 0; i < mrmLength; ++i)
            {
                try
                {
                    // 🎯 严格过滤m/z值：必须是有效的正数，且在合理范围内
                    double mass = pEventMRM->mass[i];
                    if (!std::isnan(mass) && !std::isinf(mass) && mass > 0 && mass < 10000)
                    {
                        minValidMass = qMin(minValidMass, mass);
                        maxValidMass = qMax(maxValidMass, mass);
                        validCount++;

                        // 输出前10个有效值用于调试
                        if (validCount <= 10)
                        {
                            qDebug() << "     有效m/z[" << i << "] =" << mass;
                        }
                    }
                    else if (i < 10) // 输出前10个无效值用于调试
                    {
                        qDebug() << "     无效m/z[" << i << "] =" << mass;
                    }
                }
                catch (...)
                {
                    qDebug() << "LxDataReader::getMzRangeFromSegment: 访问MRM质量数组[" << i << "]时发生异常";
                    break;
                }
            }
        }
        else
        { // Type_MRM_2048
            cParamValue::_EventMRM2048 *pEventMRM = (cParamValue::_EventMRM2048 *)pEvent;

            // 🎯 添加安全检查
            if (!pEventMRM)
            {
                qDebug() << "LxDataReader::getMzRangeFromSegment: pEventMRM2048为空指针";
                return qMakePair(0.0, 0.0);
            }

            int mrmLength = 0;
            try
            {
                mrmLength = pEventMRM->length();
                qDebug() << "LxDataReader::getMzRangeFromSegment: MRM2048事件长度:" << mrmLength;
            }
            catch (...)
            {
                qDebug() << "LxDataReader::getMzRangeFromSegment: 获取MRM2048长度时发生异常";
                return qMakePair(0.0, 0.0);
            }

            // 检查长度的合理性
            if (mrmLength < 0 || mrmLength > 10000)
            {
                qDebug() << "LxDataReader::getMzRangeFromSegment: MRM2048长度异常:" << mrmLength;
                return qMakePair(0.0, 0.0);
            }

            // 🎯 对于MRM2048，只处理前面的有效数据，不处理全部2048个
            int actualDataCount = qMin(mrmLength, 100); // 限制处理数量，避免处理无效数据

            for (int i = 0; i < actualDataCount; ++i)
            {
                try
                {
                    // 🎯 严格过滤m/z值：必须是有效的正数，且在合理范围内
                    double mass = pEventMRM->mass[i];
                    if (!std::isnan(mass) && !std::isinf(mass) && mass > 0 && mass < 10000)
                    {
                        minValidMass = qMin(minValidMass, mass);
                        maxValidMass = qMax(maxValidMass, mass);
                        validCount++;

                        // 输出前10个有效值用于调试
                        if (validCount <= 10)
                        {
                            qDebug() << "     有效m/z[" << i << "] =" << mass;
                        }
                    }
                    else if (i < 10) // 输出前10个无效值用于调试
                    {
                        qDebug() << "     无效m/z[" << i << "] =" << mass;
                    }
                }
                catch (...)
                {
                    qDebug() << "LxDataReader::getMzRangeFromSegment: 访问MRM2048质量数组[" << i << "]时发生异常";
                    break;
                }
            }
        }

        qDebug() << "LxDataReader::getMzRangeFromSegment: 范围计算结果:";
        qDebug() << "   有效质量值数量:" << validCount;
        qDebug() << "   最小值:" << minValidMass;
        qDebug() << "   最大值:" << maxValidMass;
        qDebug() << "   范围有效性检查:" << (validCount > 0) << "&&" << (maxValidMass > minValidMass);

        if (validCount > 0 && maxValidMass > minValidMass)
        {
            qDebug() << "LxDataReader::getMzRangeFromSegment: ✅ MRM事件，m/z范围:" << minValidMass << "~" << maxValidMass;
            qDebug() << "   有效质量值数量:" << validCount;
            return qMakePair(minValidMass, maxValidMass);
        }
        else
        {
            qDebug() << "LxDataReader::getMzRangeFromSegment: ❌ MRM事件没有有效的质量值";
            qDebug() << "   返回默认范围: 0.0 ~ 0.0";
            return qMakePair(0.0, 0.0);
        }
    }
    else
    {
        qDebug() << "LxDataReader::getMzRangeFromSegment: 未知事件类型:" << pEvent->type;
        return qMakePair(0.0, 0.0);
    }
}

bool LxDataReader::loadMassDataForAvg(int eventId, const QVector<int> &frameIndexVec, FileData *data)
{
    if (!data)
    {
        qDebug() << "LxDataReader::loadMassDataForAvg: FileData指针为空";
        return false;
    }

    if (frameIndexVec.isEmpty())
    {
        qDebug() << "LxDataReader::loadMassDataForAvg: 帧索引列表为空";
        return false;
    }

    QString filePath = data->getFilePath();

    // 检查文件是否存在
    if (!QFile::exists(filePath))
    {
        qDebug() << "LxDataReader::loadMassDataForAvg: 文件不存在:" << filePath;
        return false;
    }

    try
    {
        // 获取索引数组
        const std::vector<qint64> &indexArray = data->getIndexArray();
        if (indexArray.empty())
        {
            qDebug() << "LxDataReader::loadMassDataForAvg: 索引数组为空";
            return false;
        }

        // 初始化平均质谱数据
        QVector<double> avgMassX, avgMassY;
        int validFrameCount = 0;
        bool xAxisInitialized = false;
        int dataPointCount = 0;

        for (int frameIndex : frameIndexVec)
        {
            // 检查帧索引有效性
            if (frameIndex < 0 || frameIndex >= static_cast<int>(indexArray.size()))
            {
                qDebug() << "LxDataReader::loadMassDataForAvg: 无效的帧索引:" << frameIndex
                         << "，有效范围: 0~" << (indexArray.size() - 1);
                continue;
            }

            // 检查索引数组中的偏移量是否有效
            qint64 fileOffset = indexArray[frameIndex];
            if (fileOffset <= 0)
            {
                qDebug() << "LxDataReader::loadMassDataForAvg: 无效的文件偏移量:" << fileOffset
                         << "，帧索引:" << frameIndex;
                continue;
            }

            // 使用与单个MASS加载相同的方法读取数据
            QByteArray massData, streamBody;
            bool success = loadMassData(frameIndex, indexArray, massData, streamBody, filePath);

            if (!success)
            {
                qDebug() << "LxDataReader::loadMassDataForAvg: 读取帧" << frameIndex << "失败";
                continue;
            }

            // 解析质谱数据
            QVector<double> frameY;
            dataPointCount = massData.size() / sizeof(double);
            if (dataPointCount > 0)
            {
                const double *dataPtr = reinterpret_cast<const double *>(massData.data());
                frameY.reserve(dataPointCount);
                for (int i = 0; i < dataPointCount; ++i)
                {
                    frameY.append(dataPtr[i]);
                }
            }
            else
            {
                qDebug() << "LxDataReader::loadMassDataForAvg: 帧" << frameIndex << "没有有效数据";
                continue;
            }

            // 初始化X轴数据（只需要一次）- 使用与loadMassDataComplete相同的逻辑
            if (!xAxisInitialized && !frameY.isEmpty())
            {
                // 生成标准的m/z轴（与单个MASS加载保持一致）
                avgMassX.clear();
                avgMassX.reserve(dataPointCount);

                // 使用与MASS和XIC相同的方式从Segment获取真实的m/z范围
                QPair<double, double> mzRange = getMzRangeFromSegment(data, eventId);
                double startMz = mzRange.first;
                double endMz = mzRange.second;

                // 如果无法获取有效范围，无法初始化X轴
                if (startMz <= 0 || endMz <= startMz)
                {
                    qDebug() << "LxDataReader::loadMassDataForAvg: 无法从Segment获取有效的m/z范围，无法初始化X轴";
                    qDebug() << "   读取到的范围:" << startMz << "~" << endMz;
                    qDebug() << "   跳过这一帧，尝试下一帧";
                    continue; // 跳过这一帧，尝试下一帧
                }

                qDebug() << "LxDataReader::loadMassDataForAvg: 从Segment获取m/z范围:" << startMz << "~" << endMz;

                double stepMz = (endMz - startMz) / (dataPointCount - 1);

                for (int i = 0; i < dataPointCount; ++i)
                {
                    avgMassX.append(startMz + i * stepMz);
                }

                avgMassY.resize(dataPointCount);
                avgMassY.fill(0.0);
                xAxisInitialized = true;
                qDebug() << "LxDataReader::loadMassDataForAvg: X轴初始化完成，数据点数:" << avgMassX.size();
            }

            // 累加Y轴数据
            if (xAxisInitialized && frameY.size() == avgMassY.size())
            {
                for (int i = 0; i < frameY.size(); ++i)
                {
                    avgMassY[i] += frameY[i];
                }
                validFrameCount++;
                qDebug() << "LxDataReader::loadMassDataForAvg: 累加帧" << frameIndex << "完成";
            }
            else
            {
                qDebug() << "LxDataReader::loadMassDataForAvg: 帧" << frameIndex << "数据大小不匹配，跳过";
            }
        }

        if (validFrameCount == 0)
        {
            qDebug() << "LxDataReader::loadMassDataForAvg: 没有有效的帧数据";
            return false;
        }

        // 将累积数据存储到AvgMassManager
        AvgMassManager::setAvgMass(filePath, eventId, avgMassX, avgMassY, xAxisInitialized, validFrameCount);

        qDebug() << "LxDataReader::loadMassDataForAvg: 平均质谱数据加载完成";
        qDebug() << "   有效帧数:" << validFrameCount;
        qDebug() << "   数据点数:" << avgMassX.size();
        qDebug() << "   X轴范围:" << (avgMassX.isEmpty() ? 0 : avgMassX.first())
                 << "~" << (avgMassX.isEmpty() ? 0 : avgMassX.last());

        return true;
    }
    catch (const std::exception &e)
    {
        qDebug() << "LxDataReader::loadMassDataForAvg: 异常:" << e.what();
        return false;
    }
    catch (...)
    {
        qDebug() << "LxDataReader::loadMassDataForAvg: 未知异常";
        return false;
    }
}

bool LxDataReader::loadMassData(int index, const std::vector<qint64> &indexArray,
                                QByteArray &dataY, QByteArray &streamBody,
                                const QString &filePath)
{
    if (indexArray.size() <= index)
    {
        qDebug() << "LxDataReader::loadMassData: 索引超出范围" << index << "/" << indexArray.size();
        return false;
    }

    QString datFilePath = filePath;
    if (datFilePath.endsWith(".Param"))
    {
        datFilePath.replace(".Param", ".Dat");
    }
    else if (datFilePath.endsWith(".P"))
    {
        datFilePath.replace(".P", ".D");
    }

    return readMassDataBody(datFilePath, indexArray, index, index, dataY, streamBody);
}

bool LxDataReader::loadMassDataComplete(FileData *data, int frameIndex, int eventId)
{
    if (!data)
    {
        qDebug() << "LxDataReader::loadMassDataComplete: FileData指针为空";
        return false;
    }

    const std::vector<qint64> &indexArray = data->getIndexArray();
    if (indexArray.empty())
    {
        qDebug() << "LxDataReader::loadMassDataComplete: 索引数组为空";
        return false;
    }

    if (frameIndex < 0 || frameIndex >= static_cast<int>(indexArray.size()))
    {
        qDebug() << "LxDataReader::loadMassDataComplete: 帧索引超出范围" << frameIndex;
        return false;
    }

    QString filePath = data->getFilePath();
    QByteArray massData, streamBody;

    // 读取质谱数据
    if (!loadMassData(frameIndex, indexArray, massData, streamBody, filePath))
    {
        qDebug() << "LxDataReader::loadMassDataComplete: 读取质谱数据失败";
        return false;
    }

    // 🎯 解析质谱数据（使用新版本的禁止推算方法）
    QVector<double> massX, massY;
    if (!parseMassDataWithYData(data, eventId, massData, streamBody, massX, massY))
    {
        qDebug() << "LxDataReader::loadMassDataComplete: 解析质谱数据失败";
        return false;
    }

    // 获取或创建TicChartData
    TicChartData *ticData = data->getTicData(eventId);
    if (!ticData)
    {
        ticData = data->createTicData(eventId);
    }

    if (!ticData)
    {
        qDebug() << "LxDataReader::loadMassDataComplete: 无法创建TicChartData";
        return false;
    }

    // 获取或创建MassChartData
    MassChartData *massChartData = ticData->getMassData();
    if (!massChartData)
    {
        massChartData = ticData->createMassData();
    }

    if (!massChartData)
    {
        qDebug() << "LxDataReader::loadMassDataComplete: 无法创建MassChartData";
        return false;
    }

    // 设置数据到MassChartData
    massChartData->clearDataThreadSafe();
    for (int i = 0; i < massX.size() && i < massY.size(); ++i)
    {
        massChartData->appendDataPointThreadSafe(massX[i], massY[i]);
    }

    qDebug() << "LxDataReader::loadMassDataComplete: 成功加载MASS数据，数据点数:" << massX.size();
    qDebug() << "   当前线程:" << QThread::currentThread();
    qDebug() << "   主线程:" << QCoreApplication::instance()->thread();
    return true;
}

bool LxDataReader::loadXicDataBatch(FileData *data, int eventId, double mz)
{
    if (!data)
    {
        qDebug() << "LxDataReader::loadXicDataBatch: FileData指针为空";
        return false;
    }

    const std::vector<qint64> &indexArray = data->getIndexArray();
    if (indexArray.empty())
    {
        qDebug() << "LxDataReader::loadXicDataBatch: 索引数组为空";
        return false;
    }

    QString filePath = data->getFilePath();
    QString datFilePath = filePath;
    if (datFilePath.endsWith(".Param"))
    {
        datFilePath.replace(".Param", ".Dat");
    }
    else if (datFilePath.endsWith(".P"))
    {
        datFilePath.replace(".P", ".D");
    }

    // 获取m/z范围
    QPair<double, double> mzRange = getMzRangeFromSegment(data, eventId);
    double minMz = mzRange.first;
    double maxMz = mzRange.second;

    if (minMz <= 0 || maxMz <= minMz)
    {
        qDebug() << "LxDataReader::loadXicDataBatch: 无效的m/z范围:" << minMz << "~" << maxMz;
        return false;
    }

    // 设置容差
    double mzTolerance = 0.5; // 默认容差

    // 获取或创建TicChartData
    TicChartData *ticData = data->getTicData(eventId);
    if (!ticData)
    {
        qDebug() << "LxDataReader::loadXicDataBatch: 找不到对应的TIC数据，事件ID:" << eventId;
        return false;
    }

    // 创建XIC数据
    QUuid xicId = ticData->createXicData();
    XicChartData *xicData = ticData->getXicData(xicId);

    if (!xicData)
    {
        qDebug() << "LxDataReader::loadXicDataBatch: 创建XIC数据失败";
        return false;
    }

    xicData->clearDataThreadSafe();

    // 遍历所有时间点，提取XIC数据
    const std::vector<double> &ticX = data->dataTIC_X;
    for (size_t timeIndex = 0; timeIndex < ticX.size() && timeIndex < indexArray.size(); ++timeIndex)
    {
        double extractedIntensity = 0.0;

        // 🎯 修复：任何mass帧都应该可以加载出XIC，不跳过任何帧
        // 读取该时间点的质谱数据
        QByteArray massData, streamBody;
        if (loadMassData(timeIndex, indexArray, massData, streamBody, datFilePath))
        {
            // 🎯 修复：直接从原始数据中提取强度，不需要完整解析质谱数据
            int dataPointCount = massData.size() / sizeof(double);
            if (dataPointCount > 0)
            {
                const double *intensityPtr = reinterpret_cast<const double *>(massData.data());

                // 使用从Segment获取的真实m/z范围
                double mzStep = (maxMz - minMz) / (dataPointCount - 1);

                // 查找目标m/z对应的索引范围
                int targetIndex = static_cast<int>((mz - minMz) / mzStep);
                int searchRange = static_cast<int>(mzTolerance / mzStep) + 1;

                // 在容差范围内查找最大强度
                for (int i = qMax(0, targetIndex - searchRange);
                     i <= qMin(dataPointCount - 1, targetIndex + searchRange); ++i)
                {
                    extractedIntensity = qMax(extractedIntensity, intensityPtr[i]);
                }
            }
        }

        // 添加XIC数据点
        xicData->appendDataPointThreadSafe(ticX[timeIndex], extractedIntensity);
    }

    qDebug() << "LxDataReader::loadXicDataBatch: 成功加载XIC数据，m/z:" << mz
             << "，数据点数:" << xicData->getData().size();
    return true;
}

bool LxDataReader::readMassDataBody(const QString &filePath,
                                    const std::vector<qint64> &indexArray,
                                    int frameBegin, int frameEnd,
                                    QByteArray &dataY, QByteArray &streamBody)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        qDebug() << "LxDataReader::readMassDataBody: 无法打开文件" << filePath;
        return false;
    }

    if (frameBegin < 0 || frameEnd >= static_cast<int>(indexArray.size()) || frameBegin > frameEnd)
    {
        qDebug() << "LxDataReader::readMassDataBody: 帧范围无效" << frameBegin << "~" << frameEnd;
        file.close();
        return false;
    }

    dataY.clear();
    streamBody.clear();

    int accCount = 0;
    for (int frameIndex = frameBegin; frameIndex <= frameEnd; ++frameIndex)
    {
        qint64 offset = indexArray[frameIndex];
        if (offset <= 0)
        {
            qDebug() << "LxDataReader::readMassDataBody: ❌ 帧" << frameIndex << "偏移量无效:" << offset;
            qDebug() << "   这通常表示数据文件有问题，跳过此帧";
            continue;
        }

        // 定位到数据位置
        if (!file.seek(offset))
        {
            qDebug() << "LxDataReader::readMassDataBody: 无法定位到偏移量" << offset;
            continue;
        }

        // 读取StreamBody头部
        _StreamBody streamBodyHeader;
        if (file.read(reinterpret_cast<char *>(&streamBodyHeader), sizeof(_StreamBody)) != sizeof(_StreamBody))
        {
            qDebug() << "LxDataReader::readMassDataBody: 读取StreamBody头部失败";
            continue;
        }

        // 🎯 保存完整的StreamBody数据（第一次）
        if (streamBody.isEmpty())
        {
            // 计算完整StreamBody的大小：头部 + 扩展参数
            int totalStreamBodySize = sizeof(_StreamBody) + streamBodyHeader.lengthParam;

            // 重新定位到StreamBody开始位置
            if (!file.seek(offset))
            {
                qDebug() << "LxDataReader::readMassDataBody: 无法重新定位到StreamBody开始位置";
                continue;
            }

            // 读取完整的StreamBody（头部 + 扩展参数）
            streamBody.resize(totalStreamBodySize);
            if (file.read(streamBody.data(), totalStreamBodySize) != totalStreamBodySize)
            {
                qDebug() << "LxDataReader::readMassDataBody: 读取完整StreamBody失败";
                streamBody.clear();
                continue;
            }

            qDebug() << "LxDataReader::readMassDataBody: 读取完整StreamBody成功";
            qDebug() << "   StreamBody头部大小:" << sizeof(_StreamBody);
            qDebug() << "   扩展参数大小:" << streamBodyHeader.lengthParam;
            qDebug() << "   总StreamBody大小:" << totalStreamBodySize;
        }

        // 读取原始数据
        QByteArray rawData;
        qint64 dataSize = streamBodyHeader.length - sizeof(_StreamBody);
        if (dataSize > 0)
        {
            rawData.resize(dataSize);
            if (file.read(rawData.data(), dataSize) != dataSize)
            {
                qDebug() << "LxDataReader::readMassDataBody: 读取数据失败";
                continue;
            }
        }

        // 根据数据类型处理数据
        QByteArray processedData;
        switch (streamBodyHeader.typeParam)
        {
        case _StreamBody::Type_Uint16Compress:
            processedData = qUncompress(rawData);
            convertDataType<quint16>(processedData, dataY, accCount, frameBegin, frameEnd);
            break;
        case _StreamBody::Type_Uint16:
            convertDataType<quint16>(rawData, dataY, accCount, frameBegin, frameEnd);
            break;
        case _StreamBody::Type_FloatCompress:
            processedData = qUncompress(rawData);
            convertDataType<float>(processedData, dataY, accCount, frameBegin, frameEnd);
            break;
        case _StreamBody::Type_Float:
            convertDataType<float>(rawData, dataY, accCount, frameBegin, frameEnd);
            break;
        case _StreamBody::Type_DoubleCompress:
            processedData = qUncompress(rawData);
            convertDataType<double>(processedData, dataY, accCount, frameBegin, frameEnd);
            break;
        case _StreamBody::Type_Double:
            convertDataType<double>(rawData, dataY, accCount, frameBegin, frameEnd);
            break;
        default:
            qDebug() << "LxDataReader::readMassDataBody: 未知的数据类型" << streamBodyHeader.typeParam;
            break;
        }

        ++accCount;
    }

    file.close();
    return true;
}

template <typename T>
void LxDataReader::convertDataType(const QByteArray &inputData, QByteArray &outputData,
                                   int accCount, int frameBegin, int frameEnd)
{
    if (inputData.isEmpty())
    {
        return;
    }

    int dataPointCount = inputData.size() / sizeof(T);
    if (dataPointCount <= 0)
    {
        return;
    }

    const T *inputPtr = reinterpret_cast<const T *>(inputData.data());

    // 如果是第一帧，初始化输出数据
    if (accCount == 0)
    {
        outputData.resize(dataPointCount * sizeof(double));
        double *outputPtr = reinterpret_cast<double *>(outputData.data());
        for (int i = 0; i < dataPointCount; ++i)
        {
            outputPtr[i] = static_cast<double>(inputPtr[i]);
        }
    }
    else
    {
        // 累加数据
        if (outputData.size() == dataPointCount * sizeof(double))
        {
            double *outputPtr = reinterpret_cast<double *>(outputData.data());
            for (int i = 0; i < dataPointCount; ++i)
            {
                outputPtr[i] += static_cast<double>(inputPtr[i]);
            }
        }
    }

    // 如果是最后一帧，计算平均值
    if (accCount == (frameEnd - frameBegin))
    {
        double *outputPtr = reinterpret_cast<double *>(outputData.data());
        int frameCount = frameEnd - frameBegin + 1;
        for (int i = 0; i < dataPointCount; ++i)
        {
            outputPtr[i] /= frameCount;
        }
    }
}

bool LxDataReader::parseMassData(const QByteArray &dataY, const QByteArray &streamBody,
                                 QVector<double> &massX, QVector<double> &massY, FileData *data, int eventId)
{
    if (dataY.isEmpty() || streamBody.isEmpty())
    {
        qDebug() << "LxDataReader::parseMassData: 输入数据为空";
        return false;
    }

    if (streamBody.size() < sizeof(_StreamBody))
    {
        qDebug() << "LxDataReader::parseMassData: StreamBody数据不完整";
        return false;
    }

    if (!data)
    {
        qDebug() << "LxDataReader::parseMassData: FileData指针为空";
        return false;
    }

    const _StreamBody *streamBodyPtr = reinterpret_cast<const _StreamBody *>(streamBody.data());

    // 解析Y轴数据（强度）
    int dataPointCount = dataY.size() / sizeof(double);
    if (dataPointCount <= 0)
    {
        qDebug() << "LxDataReader::parseMassData: 无有效数据点";
        return false;
    }

    const double *yDataPtr = reinterpret_cast<const double *>(dataY.data());
    massY.clear();
    massY.reserve(dataPointCount);
    for (int i = 0; i < dataPointCount; ++i)
    {
        massY.append(yDataPtr[i]);
    }

    // 🎯 禁止推算：先生成真实的X轴数据，再计算范围
    massX.clear();
    massX.reserve(dataPointCount);

    // 检查当前扫描模式
    GlobalEnums::ScanMode currentScanMode = getCurrentScanMode(*data);

    qDebug() << "LxDataReader::parseMassData: ========== 禁止推算模式 ==========";
    qDebug() << "   扫描模式:" << (currentScanMode == GlobalEnums::ScanMode::MRM ? "MRM" : currentScanMode == GlobalEnums::ScanMode::SIM ? "SIM"
                                                                                                                                         : "FullScan");
    qDebug() << "   数据点数:" << dataPointCount;
    qDebug() << "   streamBody大小:" << streamBody.size();

    // 🎯 第一步：生成真实的X轴数据（不推算）

    // 🎯 根据扫描模式选择不同的X轴生成方式
    if (currentScanMode == GlobalEnums::ScanMode::MRM || currentScanMode == GlobalEnums::ScanMode::SIM)
    {
        // 🎯 离散MASS数据（MRM/SIM）：从StreamBody读取真实m/z值
        QString modeStr = (currentScanMode == GlobalEnums::ScanMode::MRM) ? "MRM" : "SIM";
        qDebug() << "LxDataReader::parseMassData: ========== " << modeStr << "离散MASS数据分析 ==========";
        qDebug() << "   dataPointCount:" << dataPointCount;
        qDebug() << "   massY数组大小:" << massY.size();
        qDebug() << "   streamBody大小:" << streamBody.size();

        // 🎯 输出前10个Y值（强度值）
        qDebug() << "   前10个强度值:";
        for (int i = 0; i < qMin(10, massY.size()); ++i)
        {
            qDebug() << "     massY[" << i << "] =" << massY[i];
        }

        // 🎯 输出StreamBody的前64个字节（十六进制）
        qDebug() << "   StreamBody前64字节（十六进制）:";
        QByteArray first64 = streamBody.left(64);
        QString hexStr;
        for (int i = 0; i < first64.size(); ++i)
        {
            hexStr += QString("%1 ").arg((unsigned char)first64[i], 2, 16, QChar('0'));
            if ((i + 1) % 16 == 0)
                hexStr += "\n     ";
        }
        qDebug() << "     " << hexStr;

        // 🎯 解析StreamBody结构
        const _StreamBody *streamBodyPtr = reinterpret_cast<const _StreamBody *>(streamBody.data());
        qDebug() << "   StreamBody结构分析:";
        qDebug() << "     length:" << streamBodyPtr->length;
        qDebug() << "     lengthParam:" << streamBodyPtr->lengthParam;
        qDebug() << "     typeParam:" << streamBodyPtr->typeParam;

        // 🎯 尝试从扩展参数中解析m/z值
        if (streamBodyPtr->lengthParam > 0 && streamBody.size() > sizeof(_StreamBody))
        {
            qDebug() << "   分析扩展参数区域:";

            // 扩展参数从StreamBody头部之后开始
            const char *extParamPtr = streamBody.data() + sizeof(_StreamBody);
            int extParamSize = streamBodyPtr->lengthParam;

            qDebug() << "     扩展参数大小:" << extParamSize;

            // 🎯 尝试解析扩展参数为double数组（可能包含m/z值）
            if (extParamSize >= sizeof(double))
            {
                int doubleCount = extParamSize / sizeof(double);
                const double *doublePtr = reinterpret_cast<const double *>(extParamPtr);

                qDebug() << "     扩展参数中的double值:";
                for (int i = 0; i < qMin(10, doubleCount); ++i)
                {
                    qDebug() << "       extParam[" << i << "] =" << doublePtr[i];
                }

                // 🎯 如果扩展参数中的double数量与数据点数量匹配，可能就是m/z值
                if (doubleCount >= dataPointCount)
                {
                    qDebug() << "   🎯 扩展参数中找到足够的double值，可能是m/z数据";
                    for (int i = 0; i < dataPointCount; ++i)
                    {
                        massX.append(doublePtr[i]);
                    }
                    qDebug() << "   使用扩展参数中的m/z值，数据点数:" << dataPointCount;
                }
                else
                {
                    qDebug() << "   扩展参数中double数量不足，使用索引";
                    for (int i = 0; i < dataPointCount; ++i)
                    {
                        massX.append(i);
                    }
                }
            }
            else
            {
                qDebug() << "   扩展参数太小，无法包含double值，使用索引";
                for (int i = 0; i < dataPointCount; ++i)
                {
                    massX.append(i);
                }
            }
        }
        else
        {
            qDebug() << "   没有扩展参数，使用索引";
            for (int i = 0; i < dataPointCount; ++i)
            {
                massX.append(i); // 临时使用索引，避免推算
            }
        }

        qDebug() << "LxDataReader::parseMassData: " << modeStr << "数据m/z解析完成，数据点数:" << dataPointCount;
        qDebug() << "==========================================";
    }
    else
    {
        // 🎯 FullScan数据：也要从实际数据中读取真实m/z值，禁止推算
        qDebug() << "LxDataReader::parseMassData: ========== FullScan数据分析（禁止推算） ==========";
        qDebug() << "   dataPointCount:" << dataPointCount;
        qDebug() << "   streamBody大小:" << streamBody.size();

        // 🎯 尝试从StreamBody中读取FullScan的真实m/z值
        bool foundRealMzValues = false;

        // 解析StreamBody结构
        if (streamBody.size() >= sizeof(_StreamBody))
        {
            const _StreamBody *streamBodyPtr = reinterpret_cast<const _StreamBody *>(streamBody.data());
            qDebug() << "   StreamBody结构分析:";
            qDebug() << "     length:" << streamBodyPtr->length;
            qDebug() << "     lengthParam:" << streamBodyPtr->lengthParam;

            // 检查扩展参数中是否有m/z数据
            if (streamBodyPtr->lengthParam > 0 && streamBody.size() > sizeof(_StreamBody))
            {
                const char *extParamPtr = streamBody.data() + sizeof(_StreamBody);
                int extParamSize = streamBodyPtr->lengthParam;

                qDebug() << "     扩展参数大小:" << extParamSize;

                // 尝试解析为double数组
                if (extParamSize >= sizeof(double))
                {
                    int doubleCount = extParamSize / sizeof(double);
                    const double *doublePtr = reinterpret_cast<const double *>(extParamPtr);

                    qDebug() << "     扩展参数中的前10个double值:";
                    for (int i = 0; i < qMin(10, doubleCount); ++i)
                    {
                        qDebug() << "       extParam[" << i << "] =" << doublePtr[i];
                    }

                    // 如果有足够的double值，使用作为m/z
                    if (doubleCount >= dataPointCount)
                    {
                        for (int i = 0; i < dataPointCount; ++i)
                        {
                            massX.append(doublePtr[i]);
                        }
                        foundRealMzValues = true;
                        qDebug() << "   ✅ 从扩展参数读取到真实m/z值，数据点数:" << dataPointCount;
                    }
                }
            }
        }

        // 如果没有找到真实m/z值，使用索引（避免推算）
        if (!foundRealMzValues)
        {
            qDebug() << "   ❌ 未找到真实m/z值，使用索引避免推算";
            for (int i = 0; i < dataPointCount; ++i)
            {
                massX.append(i);
            }
        }

        qDebug() << "==========================================";
    }

    // 🎯 从生成的X轴数据计算实际范围用于日志显示
    double actualStartMz = 0.0, actualEndMz = 0.0;
    if (massX.size() > 0)
    {
        actualStartMz = massX.first();
        actualEndMz = massX.last();
    }

    qDebug() << "LxDataReader::parseMassData: 解析完成，数据点数:" << dataPointCount
             << "，m/z范围:" << actualStartMz << "~" << actualEndMz;
    return true;
}

/**
 * @brief 从StreamBody中解析m/z范围
 * @param streamBody StreamBody数据
 * @return m/z范围的QPair，first为起始值，second为结束值
 */
QPair<double, double> LxDataReader::parseMzRangeFromStreamBody(const QByteArray &streamBody)
{
    if (streamBody.size() < sizeof(_StreamBody))
    {
        qDebug() << "LxDataReader::parseMzRangeFromStreamBody: StreamBody数据不完整";
        return qMakePair(0.0, 0.0);
    }

    const _StreamBody *streamBodyPtr = reinterpret_cast<const _StreamBody *>(streamBody.data());

    // 尝试从StreamBody结构中提取m/z范围信息
    // 注意：这里需要根据实际的_StreamBody结构来实现
    // 如果StreamBody中没有直接的m/z范围信息，返回无效范围

    // TODO: 根据实际的_StreamBody结构实现m/z范围提取
    // 目前返回无效范围，让调用者使用推算方式

    qDebug() << "LxDataReader::parseMzRangeFromStreamBody: StreamBody中暂无m/z范围信息，返回无效范围";
    return qMakePair(0.0, 0.0);
}

bool LxDataReader::parseMassData(FileData *data, int eventId, const QByteArray &streamBody, QVector<double> &massX, QVector<double> &massY)
{
    // 🎯 禁止推算：先生成真实的X轴数据，再计算范围
    massX.clear();
    massY.clear();

    // 检查当前扫描模式
    GlobalEnums::ScanMode currentScanMode = getCurrentScanMode(*data);

    qDebug() << "LxDataReader::parseMassData: ========== 禁止推算模式 ==========";
    qDebug() << "   扫描模式:" << (currentScanMode == GlobalEnums::ScanMode::MRM ? "MRM" : currentScanMode == GlobalEnums::ScanMode::SIM ? "SIM"
                                                                                                                                         : "FullScan");
    qDebug() << "   streamBody大小:" << streamBody.size();

    // 🎯 第一步：解析StreamBody结构
    if (streamBody.size() < sizeof(_StreamBody))
    {
        qDebug() << "LxDataReader::parseMassData: StreamBody数据不完整";
        return false;
    }

    const _StreamBody *streamBodyPtr = reinterpret_cast<const _StreamBody *>(streamBody.data());
    qDebug() << "LxDataReader::parseMassData: StreamBody结构分析:";
    qDebug() << "   length:" << streamBodyPtr->length;
    qDebug() << "   lengthParam:" << streamBodyPtr->lengthParam;
    qDebug() << "   typeParam:" << streamBodyPtr->typeParam;

    // 🎯 第二步：根据扫描模式选择不同的数据读取策略
    if (currentScanMode == GlobalEnums::ScanMode::MRM || currentScanMode == GlobalEnums::ScanMode::SIM)
    {
        // 🎯 MRM/SIM数据：需要结合Segment和StreamBody数据
        if (!parseMRMSIMData(streamBodyPtr, streamBody, massX, massY))
        {
            return false;
        }

        // 🎯 从Segment中获取真实的m/z值
        return getMRMRealMzValues(data, eventId, massX);
    }
    else
    {
        return parseFullScanData(streamBodyPtr, streamBody, massX, massY);
    }
}

bool LxDataReader::parseMRMSIMData(const _StreamBody *streamBodyPtr, const QByteArray &streamBody, QVector<double> &massX, QVector<double> &massY)
{
    qDebug() << "LxDataReader::parseMRMSIMData: 开始解析MRM/SIM数据";

    // 🎯 MRM/SIM数据：m/z值存储在Segment的_EventMRM结构中，不在StreamBody中
    qDebug() << "   MRM/SIM的m/z值需要从Segment中的_EventMRM结构读取";
    qDebug() << "   StreamBody只包含强度数据，不包含m/z值";

    // 🎯 先解析Y轴数据（强度值）获取数据点数量
    if (!parseIntensityData(streamBodyPtr, streamBody, massY, 0))
    {
        return false;
    }

    int dataPointCount = massY.size();
    qDebug() << "   解析到" << dataPointCount << "个强度值";

    // 🎯 暂时使用索引作为m/z值，真实的m/z值需要从Segment中获取
    qDebug() << "   警告：暂时使用索引作为m/z值，需要从Segment中获取真实m/z值";
    for (int i = 0; i < dataPointCount; ++i)
    {
        massX.append(i);
    }

    qDebug() << "   生成" << massX.size() << "个m/z索引值";
    return true;
}

bool LxDataReader::parseFullScanData(const _StreamBody *streamBodyPtr, const QByteArray &streamBody, QVector<double> &massX, QVector<double> &massY)
{
    qDebug() << "LxDataReader::parseFullScanData: 开始解析FullScan数据";

    // 🎯 FullScan数据：需要从实际数据中提取m/z信息
    // 先解析Y轴数据获取数据点数量
    if (!parseIntensityData(streamBodyPtr, streamBody, massY, 0))
    {
        return false;
    }

    int dataPointCount = massY.size();
    qDebug() << "   FullScan数据点数量:" << dataPointCount;

    // 🎯 对于FullScan，尝试从扩展参数中读取起始m/z和步长
    if (streamBodyPtr->lengthParam >= 2 * sizeof(double))
    {
        const char *extParamPtr = streamBody.data() + sizeof(_StreamBody);
        const double *doublePtr = reinterpret_cast<const double *>(extParamPtr);

        double startMz = doublePtr[0];
        double stepMz = doublePtr[1];

        qDebug() << "   从扩展参数读取: startMz=" << startMz << ", stepMz=" << stepMz;

        // 🎯 使用真实的起始值和步长生成m/z序列
        if (startMz > 0 && stepMz > 0)
        {
            for (int i = 0; i < dataPointCount; ++i)
            {
                massX.append(startMz + i * stepMz);
            }
            qDebug() << "   生成" << massX.size() << "个m/z值，范围:" << massX.first() << "~" << massX.last();
            return true;
        }
    }

    // 🎯 如果无法从扩展参数获取，尝试从Segment获取m/z范围
    qDebug() << "   无法从扩展参数获取m/z信息，尝试从Segment获取范围";

    // 这里需要传入FileData和eventId来获取Segment信息
    // 暂时使用索引，但这不是最终解决方案
    qDebug() << "   警告：FullScan数据暂时使用索引，需要传入FileData来获取真实范围";
    for (int i = 0; i < dataPointCount; ++i)
    {
        massX.append(i);
    }

    return true;
}

bool LxDataReader::parseIntensityData(const _StreamBody *streamBodyPtr, const QByteArray &streamBody, QVector<double> &massY, int expectedCount)
{
    qDebug() << "LxDataReader::parseIntensityData: 开始解析强度数据";
    qDebug() << "   typeParam:" << streamBodyPtr->typeParam;
    qDebug() << "   length:" << streamBodyPtr->length;
    qDebug() << "   lengthParam:" << streamBodyPtr->lengthParam;

    // 计算实际数据区域的位置和大小
    int headerSize = sizeof(_StreamBody);
    int paramSize = streamBodyPtr->lengthParam;
    int dataOffset = headerSize + paramSize;
    int dataSize = streamBody.size() - dataOffset;

    qDebug() << "   数据偏移:" << dataOffset << ", 数据大小:" << dataSize;

    if (dataSize <= 0)
    {
        qDebug() << "   没有强度数据";
        return false;
    }

    const char *dataPtr = streamBody.data() + dataOffset;

    // 🎯 根据typeParam解析不同格式的数据
    switch (streamBodyPtr->typeParam)
    {
    case _StreamBody::Type_Uint16:
        return parseTypedIntensityData<quint16>(dataPtr, dataSize, massY, expectedCount, false);
    case _StreamBody::Type_Uint16Compress:
        return parseTypedIntensityData<quint16>(dataPtr, dataSize, massY, expectedCount, true);
    case _StreamBody::Type_Float:
        return parseTypedIntensityData<float>(dataPtr, dataSize, massY, expectedCount, false);
    case _StreamBody::Type_FloatCompress:
        return parseTypedIntensityData<float>(dataPtr, dataSize, massY, expectedCount, true);
    case _StreamBody::Type_Double:
        return parseTypedIntensityData<double>(dataPtr, dataSize, massY, expectedCount, false);
    case _StreamBody::Type_DoubleCompress:
        return parseTypedIntensityData<double>(dataPtr, dataSize, massY, expectedCount, true);
    case _StreamBody::Type_Uint32:
        return parseTypedIntensityData<quint32>(dataPtr, dataSize, massY, expectedCount, false);
    case _StreamBody::Type_Uint32Compress:
        return parseTypedIntensityData<quint32>(dataPtr, dataSize, massY, expectedCount, true);
    case _StreamBody::Type_Uint8:
        return parseTypedIntensityData<quint8>(dataPtr, dataSize, massY, expectedCount, false);
    case _StreamBody::Type_Uint8Compress:
        return parseTypedIntensityData<quint8>(dataPtr, dataSize, massY, expectedCount, true);
    default:
        qDebug() << "   未知的数据类型:" << streamBodyPtr->typeParam;
        return false;
    }
}

template <typename T>
bool LxDataReader::parseTypedIntensityData(const char *dataPtr, int dataSize, QVector<double> &massY, int expectedCount, bool isCompressed)
{
    qDebug() << "LxDataReader::parseTypedIntensityData: 解析类型化强度数据";
    qDebug() << "   数据类型大小:" << sizeof(T) << ", 是否压缩:" << isCompressed;

    QByteArray processedData;

    if (isCompressed)
    {
        // 🎯 解压缩数据
        QByteArray compressedData(dataPtr, dataSize);
        processedData = qUncompress(compressedData);

        if (processedData.isEmpty())
        {
            qDebug() << "   数据解压缩失败";
            return false;
        }

        qDebug() << "   解压缩后数据大小:" << processedData.size();
    }
    else
    {
        // 🎯 直接使用原始数据
        processedData = QByteArray(dataPtr, dataSize);
    }

    // 🎯 解析数据
    int elementCount = processedData.size() / sizeof(T);
    const T *typedPtr = reinterpret_cast<const T *>(processedData.data());

    qDebug() << "   数据元素数量:" << elementCount;

    // 如果指定了期望数量，进行验证
    if (expectedCount > 0 && elementCount != expectedCount)
    {
        qDebug() << "   警告：数据数量不匹配，期望:" << expectedCount << ", 实际:" << elementCount;
    }

    // 🎯 转换为double并存储
    massY.clear();
    massY.reserve(elementCount);

    for (int i = 0; i < elementCount; ++i)
    {
        massY.append(static_cast<double>(typedPtr[i]));
    }

    qDebug() << "   成功解析" << massY.size() << "个强度值";

    // 输出前5个值用于调试
    for (int i = 0; i < qMin(5, massY.size()); ++i)
    {
        qDebug() << "     massY[" << i << "] =" << massY[i];
    }

    return true;
}

bool LxDataReader::getMRMRealMzValues(FileData *data, int eventId, QVector<double> &massX)
{
    qDebug() << "LxDataReader::getMRMRealMzValues: 从Segment获取MRM真实m/z值";
    qDebug() << "   事件ID:" << eventId;
    qDebug() << "   当前massX大小:" << massX.size();

    // 检查FileData是否有Segment信息
    if (data->mSegment.empty())
    {
        qDebug() << "   没有Segment信息，无法获取真实m/z值";
        return true; // 保持索引值
    }

    // 解析第一个Segment的QByteArray数据
    const QByteArray &segmentData = data->mSegment[0];
    if (segmentData.size() < sizeof(cParamValue::_Segment))
    {
        qDebug() << "   Segment数据不完整";
        return true; // 保持索引值
    }

    // 将QByteArray转换为_Segment结构体
    const cParamValue::_Segment *pSegment = reinterpret_cast<const cParamValue::_Segment *>(segmentData.constData());

    if (pSegment->countsEvent == 0)
    {
        qDebug() << "   Segment中没有事件";
        return true; // 保持索引值
    }

    // 找到指定的事件
    int offsetP = 0;
    for (uint32_t currentEvt = 0; currentEvt < pSegment->countsEvent; ++currentEvt)
    {
        const cParamValue::_Event *pEvent = reinterpret_cast<const cParamValue::_Event *>((char *)&(pSegment->fisrtEvent) + offsetP);

        if (currentEvt == eventId)
        {
            // 找到目标事件，提取m/z值
            if (pEvent->type == cParamValue::Type_MRM)
            {
                const cParamValue::_EventMRM *pEventMRM = reinterpret_cast<const cParamValue::_EventMRM *>(pEvent);
                return extractMRMMzValues(pEventMRM, massX);
            }
            else if (pEvent->type == cParamValue::Type_MRM_2048)
            {
                const cParamValue::_EventMRM2048 *pEventMRM = reinterpret_cast<const cParamValue::_EventMRM2048 *>(pEvent);
                return extractMRM2048MzValues(pEventMRM, massX);
            }
            else if (pEvent->type == cParamValue::Type_SIM)
            {
                const cParamValue::_EventSIM *pEventSIM = reinterpret_cast<const cParamValue::_EventSIM *>(pEvent);
                return extractSIMMzValues(pEventSIM, massX);
            }
            else
            {
                qDebug() << "   事件类型不是MRM/SIM:" << pEvent->type;
                return true; // 保持索引值
            }
        }

        // 更新偏移量到下一个事件
        if (pEvent->type == cParamValue::Type_MRM)
        {
            offsetP += sizeof(cParamValue::_EventMRM);
        }
        else if (pEvent->type == cParamValue::Type_MRM_2048)
        {
            offsetP += sizeof(cParamValue::_EventMRM2048);
        }
        else if (pEvent->type == cParamValue::Type_SIM)
        {
            offsetP += sizeof(cParamValue::_EventSIM);
        }
        else
        {
            offsetP += sizeof(cParamValue::_EventLIT);
        }
    }

    qDebug() << "   未找到指定的事件ID:" << eventId;
    return true; // 保持索引值
}

bool LxDataReader::extractMRMMzValues(const cParamValue::_EventMRM *pEventMRM, QVector<double> &massX)
{
    qDebug() << "LxDataReader::extractMRMMzValues: 提取MRM m/z值";

    if (!pEventMRM)
    {
        qDebug() << "   pEventMRM为空指针";
        return true;
    }

    try
    {
        int mrmLength = pEventMRM->length();
        qDebug() << "   MRM事件长度:" << mrmLength;

        if (mrmLength <= 0 || mrmLength > 10000)
        {
            qDebug() << "   MRM长度异常:" << mrmLength;
            return true;
        }

        // 🎯 替换索引值为真实的m/z值
        QVector<double> realMzValues;
        for (int i = 0; i < mrmLength && i < massX.size(); ++i)
        {
            double mass = pEventMRM->mass[i];
            // 🎯 严格过滤：必须是有效的正数，且在合理范围内
            if (!std::isnan(mass) && !std::isinf(mass) && mass > 0 && mass < 10000)
            {
                realMzValues.append(mass);
                qDebug() << "     MRM m/z[" << i << "] =" << mass;
            }
            else
            {
                qDebug() << "     MRM m/z[" << i << "] = 无效值:" << mass;
                realMzValues.append(i); // 保持索引
            }
        }

        // 🎯 只替换有效的m/z值
        if (realMzValues.size() == massX.size())
        {
            massX = realMzValues;
            qDebug() << "   ✅ 成功替换为真实MRM m/z值，数量:" << massX.size();
        }
        else
        {
            qDebug() << "   ❌ m/z值数量不匹配，保持索引值";
            qDebug() << "     期望:" << massX.size() << ", 实际:" << realMzValues.size();
        }
    }
    catch (...)
    {
        qDebug() << "   提取MRM m/z值时发生异常";
    }

    return true;
}

bool LxDataReader::extractMRM2048MzValues(const cParamValue::_EventMRM2048 *pEventMRM, QVector<double> &massX)
{
    qDebug() << "LxDataReader::extractMRM2048MzValues: 提取MRM2048 m/z值";

    if (!pEventMRM)
    {
        qDebug() << "   pEventMRM2048为空指针";
        return true;
    }

    try
    {
        int mrmLength = pEventMRM->length();
        qDebug() << "   MRM2048事件长度:" << mrmLength;

        if (mrmLength <= 0 || mrmLength > 10000)
        {
            qDebug() << "   MRM2048长度异常:" << mrmLength;
            return true;
        }

        // 🎯 只提取需要的m/z值，不提取所有2048个
        QVector<double> realMzValues;
        int extractCount = qMin(mrmLength, massX.size());

        qDebug() << "   需要提取的m/z数量:" << extractCount << "（总长度:" << mrmLength << "）";

        for (int i = 0; i < extractCount; ++i)
        {
            double mass = pEventMRM->mass[i];
            // 🎯 严格过滤：必须是有效的正数，且在合理范围内
            if (!std::isnan(mass) && !std::isinf(mass) && mass > 0 && mass < 10000)
            {
                realMzValues.append(mass);
                if (i < 10) // 只输出前10个用于调试
                {
                    qDebug() << "     MRM2048 m/z[" << i << "] =" << mass;
                }
            }
            else
            {
                if (i < 10)
                {
                    qDebug() << "     MRM2048 m/z[" << i << "] = 无效值:" << mass;
                }
                realMzValues.append(i); // 保持索引
            }
        }

        // 🎯 只替换有效的m/z值
        if (realMzValues.size() == massX.size())
        {
            massX = realMzValues;
            qDebug() << "   ✅ 成功替换为真实MRM2048 m/z值，数量:" << massX.size();
        }
        else
        {
            qDebug() << "   ❌ m/z值数量不匹配，保持索引值";
            qDebug() << "     期望:" << massX.size() << ", 实际:" << realMzValues.size();
        }
    }
    catch (...)
    {
        qDebug() << "   提取MRM2048 m/z值时发生异常";
    }

    return true;
}

bool LxDataReader::extractSIMMzValues(const cParamValue::_EventSIM *pEventSIM, QVector<double> &massX)
{
    qDebug() << "LxDataReader::extractSIMMzValues: 提取SIM m/z值";

    if (!pEventSIM)
    {
        qDebug() << "   pEventSIM为空指针";
        return true;
    }

    try
    {
        int simLength = pEventSIM->length();
        qDebug() << "   SIM事件长度:" << simLength;

        if (simLength <= 0 || simLength > 10000)
        {
            qDebug() << "   SIM长度异常:" << simLength;
            return true;
        }

        // 🎯 替换索引值为真实的m/z值
        QVector<double> realMzValues;
        for (int i = 0; i < simLength && i < massX.size(); ++i)
        {
            double mass = pEventSIM->mass[i];
            if (!std::isnan(mass) && !std::isinf(mass) && mass > 0)
            {
                realMzValues.append(mass);
                qDebug() << "     SIM m/z[" << i << "] =" << mass;
            }
            else
            {
                qDebug() << "     SIM m/z[" << i << "] = 无效值:" << mass;
                realMzValues.append(i); // 保持索引
            }
        }

        // 🎯 只替换有效的m/z值
        if (realMzValues.size() == massX.size())
        {
            massX = realMzValues;
            qDebug() << "   ✅ 成功替换为真实SIM m/z值，数量:" << massX.size();
        }
        else
        {
            qDebug() << "   ❌ m/z值数量不匹配，保持索引值";
            qDebug() << "     期望:" << massX.size() << ", 实际:" << realMzValues.size();
        }
    }
    catch (...)
    {
        qDebug() << "   提取SIM m/z值时发生异常";
    }

    return true;
}

bool LxDataReader::parseMassDataWithYData(FileData *data, int eventId, const QByteArray &massData, const QByteArray &streamBody, QVector<double> &massX, QVector<double> &massY)
{
    qDebug() << "LxDataReader::parseMassDataWithYData: 解析质谱数据（包含Y轴数据）";
    qDebug() << "   massData大小:" << massData.size();
    qDebug() << "   streamBody大小:" << streamBody.size();

    // 🎯 第一步：解析Y轴数据（强度）
    massY.clear();
    int dataPointCount = massData.size() / sizeof(double);
    if (dataPointCount <= 0)
    {
        qDebug() << "   无有效数据点";
        return false;
    }

    const double *yDataPtr = reinterpret_cast<const double *>(massData.data());
    massY.reserve(dataPointCount);
    for (int i = 0; i < dataPointCount; ++i)
    {
        massY.append(yDataPtr[i]);
    }

    qDebug() << "   解析到" << massY.size() << "个强度值";

    // 🎯 第二步：根据扫描模式生成X轴数据
    GlobalEnums::ScanMode currentScanMode = getCurrentScanMode(*data);

    if (currentScanMode == GlobalEnums::ScanMode::MRM || currentScanMode == GlobalEnums::ScanMode::SIM)
    {
        // 🎯 MRM/SIM数据：从Segment获取真实m/z值
        qDebug() << "   MRM/SIM数据，从Segment获取真实m/z值";

        // 先用索引初始化
        massX.clear();
        massX.reserve(dataPointCount);
        for (int i = 0; i < dataPointCount; ++i)
        {
            massX.append(i);
        }

        // 然后从Segment获取真实m/z值
        getMRMRealMzValues(data, eventId, massX);
    }
    else
    {
        // 🎯 FullScan数据：从Segment获取m/z范围，生成连续序列
        qDebug() << "   FullScan数据，从Segment获取m/z范围";

        QPair<double, double> mzRange = getMzRangeFromSegment(data, eventId);
        double startMz = mzRange.first;
        double endMz = mzRange.second;

        qDebug() << "   从Segment获取m/z范围:" << startMz << "~" << endMz;

        massX.clear();
        massX.reserve(dataPointCount);

        if (startMz > 0 && endMz > startMz && dataPointCount > 1)
        {
            // 🎯 使用Segment中的真实范围生成连续序列
            double stepMz = (endMz - startMz) / (dataPointCount - 1);
            for (int i = 0; i < dataPointCount; ++i)
            {
                massX.append(startMz + i * stepMz);
            }
            qDebug() << "   生成连续m/z序列，步长:" << stepMz;
        }
        else
        {
            // 如果无法获取有效范围，使用索引
            qDebug() << "   无法获取有效m/z范围，使用索引";
            for (int i = 0; i < dataPointCount; ++i)
            {
                massX.append(i);
            }
        }
    }

    qDebug() << "   最终m/z范围:" << (massX.isEmpty() ? 0.0 : massX.first()) << "~" << (massX.isEmpty() ? 0.0 : massX.last());

    // 🎯 添加数据验证，确保X和Y数据一致
    if (massX.size() != massY.size())
    {
        qDebug() << "   ❌ 警告：X轴和Y轴数据数量不匹配！";
        qDebug() << "     X轴数量:" << massX.size() << ", Y轴数量:" << massY.size();

        // 截断到较小的数量，确保数据一致性
        int minSize = qMin(massX.size(), massY.size());
        if (minSize > 0)
        {
            massX.resize(minSize);
            massY.resize(minSize);
            qDebug() << "   已截断到" << minSize << "个数据点";
        }
        else
        {
            qDebug() << "   数据无效，返回失败";
            return false;
        }
    }

    qDebug() << "   ✅ 数据验证通过，X轴:" << massX.size() << "个，Y轴:" << massY.size() << "个";
    return true;
}
