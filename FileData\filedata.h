#ifndef FILEDATA_H
#define FILEDATA_H

#include <QObject>
#include <QVector>
#include <QMap>
#include <QPointF>
#include "LxChart/lxchartdata.h"
#include "LxChart/ticchartdata.h"
#include "LxChart/xicchartdata.h"
#include "LxChart/masschartdata.h"
#include "sMethod/cConfigOMS.h"
#include "method/cParamCCS.h"

// 移除Experiment结构体，现在使用TicChartData管理所有数据

// 移除MassData、TicData、XicData结构体
// 这些数据现在直接集成到对应的ChartData类中

class FileData : public QObject
{
    Q_OBJECT
public:
    explicit FileData(QString ParamPath, QObject *parent = nullptr);
    ~FileData();

    QList<QByteArray> mSegment;
    // bool isRestart = true;

    _CONGIG_OMS::_PARAM_FIT mCALIBRATE;

    QList<QList<std::vector<double>>> tmpThreadBuffX;
    QList<QList<std::vector<double>>> tmpThreadBuffY;

    QList<std::vector<quint32>> mPointTimeSIM;

    // 中间数据结构
    std::vector<qint64> indexArray;
    std::vector<double> dataTIC_X;
    std::vector<double> dataTIC_Y;
    QList<std::vector<double>> otherLinesY;
    QByteArray streamHead;
    QVector<qint64> pageTIC;

    // 新解析方式的XIC映射
    QMap<uint32_t, QMap<QString, _PARAM_XIC *>> xicMap;

    // 按照示例项目添加的数据结构
    QList<QByteArray> mStreamHead; // 流头数据列表（多文件支持）
    QList<QByteArray> mStreamBody; // 流体数据列表（多文件支持）
    QList<QString> mStrProperty;   // 属性字符串列表
    QList<QString> mTuneFilePath;  // 调谐文件路径列表

    // 加载TIC数据参数数组
    QVector<double> timePoints;
    QVector<double> intensities;
    QVector<qint64> frameIndices;

    // 核心数据管理 - 只保留这一个Map，建立清晰的层级关系
    QMap<int, TicChartData *> TicMap; // key: eventId, value: TIC数据（包含MASS和XIC）

    // 移除以下成员变量，简化数据结构：
    // - XicData xic_data;
    // - QMap<int, QMap<int, MassChartData *>> MASS_Map;
    // - QMap<double, Experiment *> ExperimentMap;
    // - XicChartData *xicChartData;

    // 已加载的MASS数据计数
    int readMassCount = 0;

    QString getFilePath() const;

    // 移除getExperimentPoint方法，因为ExperimentMap已被移除
    // Experiment *getExperimentPoint(double id);

    // 新的TIC数据管理方法
    TicChartData *getTicData(int eventId) const;
    TicChartData *createTicData(int eventId);
    void removeTicData(int eventId);
    QList<int> getAllEventIds() const;

    // 按照示例项目添加的数据访问方法
    QList<QByteArray> &getStreamHeadList() { return mStreamHead; }
    QList<QByteArray> &getStreamBodyList() { return mStreamBody; }
    QList<QString> &getPropertyList() { return mStrProperty; }
    QList<QString> &getTuneFilePathList() { return mTuneFilePath; }

    // XIC映射访问方法
    QMap<uint32_t, QMap<QString, _PARAM_XIC *>> &getXicMap() { return xicMap; }
    const QMap<uint32_t, QMap<QString, _PARAM_XIC *>> &getXicMap() const { return xicMap; }

    // 索引数组访问方法（用于MASS数据读取）
    void setIndexArray(const std::vector<qint64> &indexArray) { this->indexArray = indexArray; }
    const std::vector<qint64> &getIndexArray() const { return indexArray; }

private:
    QString m_qstr_filePath; // 文件绝对路径
signals:
    void readFileSuccess(bool flag); // 读取文件成功信号 flag:true 成功 false失败
    void updateMass(bool flag);      // 刷新某帧Mass信号，flag:true 成功 false失败
};

#endif // FILEDATA_H
