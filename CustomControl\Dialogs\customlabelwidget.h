#ifndef CUSTOMLABELWIDGET_H
#define CUSTOMLABELWIDGET_H

#include <QWidget>
#include <qdebug.h>
#include <QMouseEvent>
#include <QGraphicsEllipseItem>
namespace Ui {
class CustomLabelWidget;
}

class CustomLabelWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CustomLabelWidget(QString content, QFont font, QColor color, QWidget *parent = nullptr);
    ~CustomLabelWidget();
    bool m_bool_isHover = false;
    QPointF getPoint() const;
    void setPoint(QPointF newPoint);

    void connectAll();

    // 显示/隐藏标注区域
    void showLabel();
    void hideLabel();

    // 获取与设置对应的标记点
    QGraphicsEllipseItem *getMarker() const;
    void setMarker(QGraphicsEllipseItem *marker);

    // 判断标注是否显示
    bool isLabelVisible() const;
    QString m_qstr_content;

signals:
    void labelMoved(CustomLabelWidget *widget);         // 标签被移动的信号
    void showLabelRequested(CustomLabelWidget *widget); // 请求显示标签的信号
    void hideLabelRequested(CustomLabelWidget *widget); // 请求隐藏标签的信号
    void deleteLabel();                                 // 删除标签

protected:
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    // bool eventFilter(QObject *obj, QEvent *event) override;
    void enterEvent(QEvent *e) override;
    void leaveEvent(QEvent *e) override;

private:
    QPointF point;
    QPoint lastPos;
    QFont font;
    QColor color;
    bool m_bool_isDragging = false;
    QGraphicsEllipseItem *m_marker = nullptr; // 对应的标记点

private:
    Ui::CustomLabelWidget *ui;
};

#endif // CUSTOMLABELWIDGET_H
