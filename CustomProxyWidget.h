#ifndef CUSTOMPROXYWIDGET_H
#define CUSTOMPROXYWIDGET_H

#include <QGraphicsProxyWidget>
#include <QGraphicsSceneMouseEvent>
#include <QMouseEvent>
#include <QApplication>
#include <QDebug>

class CustomProxyWidget : public QGraphicsProxyWidget
{
    Q_OBJECT

public:
    CustomProxyWidget(QGraphicsItem *parent = nullptr);
    
protected:
    // 重写场景事件处理
    bool sceneEvent(QEvent *event) override;
    
    // 重写特定类型的鼠标事件处理
    bool sceneMousePressEvent(QGraphicsSceneMouseEvent *event);
    bool sceneMouseReleaseEvent(QGraphicsSceneMouseEvent *event);
    bool sceneMouseMoveEvent(QGraphicsSceneMouseEvent *event);
    bool sceneMouseDoubleClickEvent(QGraphicsSceneMouseEvent *event);
    
    // 将场景事件转换为控件事件并发送
    bool sendMouseEventToWidget(QGraphicsSceneMouseEvent *event, QEvent::Type type);
};

#endif // CUSTOMPROXYWIDGET_H