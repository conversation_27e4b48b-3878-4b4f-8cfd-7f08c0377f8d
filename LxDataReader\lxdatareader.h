#ifndef LXDATAREADER_H
#define LXDATAREADER_H

#include <QObject>
#include <QFile>
#include <QDebug>
#include <QDateTime>
#include <QByteArray>
#include <QVector>
#include <QList>
#include <QMap>
#include <vector>
// 移除DLL依赖的头文件
// #include "LibDataFileD/include/cDataFileRead.h"
#include "LibDataFilterR/include/cDataFilter.h"
#include "sMethod/cConfigOMS.h"
#include "method/cParamCCS.h"
#include "LibPeakAlgorithmR/include/cPeakAlgorithm.h"
#include "FileData/filedata.h"
#include "Globals/GlobalDefine.h"
#include <QtConcurrent>
#include <QtAlgorithms>

// 包含cParamValue定义
#ifdef Q_OS_WIN32
#include "D:/QT/GlobalStruct/cPublicCCS.h"
#else
#include "/home/<USER>/work/GlobalStruct/cPublicCCS.h"
#endif

// MRM数据结构体（从MRMReader迁移）
struct StructMRM
{
    uint Experiment;
    QString ID;
    double Q1;
    double Q3;
    double RT;
};

/**
 * @brief 统一数据读取器 - 集成所有数据读取功能
 *
 * 这个类集成了原来分散在LxCustomDataReader和MRMReader中的所有功能：
 * 1. 不依赖任何外部DLL
 * 2. 完整读取所有数据，无Period限制
 * 3. 支持大文件处理
 * 4. 内存优化的数据读取
 * 5. 扫描类型检测和MRM数据解析
 * 6. 平均质谱数据处理
 */
class LxDataReader : public QObject
{
    Q_OBJECT
public:
    explicit LxDataReader(QObject *parent = nullptr);
    ~LxDataReader();

    // ========== 原有接口（保持兼容性） ==========
    // 整页读取Tic数据
    bool loadFileTicFullPage(FileData *data);
    bool loadFileMass(int index,                              // in<-帧数据位置
                      const std::vector<qint64> &pIndexArray, // in<-帧数据起始位置表
                      QByteArray &pDataY,                     // out->数据序列
                      QByteArray &pStreamBody,                // out->参数序列
                      QString pFilePath = QString()           // in<-完整文件路径，若为空，默认为应用程序当前路径+"/data/tmp.D"
    );

    // ========== 从LxCustomDataReader迁移的方法 ==========
    // 完整TIC数据加载
    bool loadTICDataComplete(FileData *data);

    // 完整的MASS数据加载和设置到FileData
    bool loadMassDataComplete(FileData *data, int frameIndex, int eventId);

    // 从StreamHead中解析Segment信息
    bool parseSegmentFromStreamHead(const QByteArray &streamHead, FileData *data);

    // 从Segment获取m/z范围（用于XIC和MASS）
    QPair<double, double> getMzRangeFromSegment(FileData *data, int eventId);

    // 批量加载XIC数据
    bool loadXicDataBatch(FileData *data, int eventId, double mz);

    // 加载平均质谱数据
    bool loadMassDataForAvg(int eventId, const QVector<int> &frameIndexVec, FileData *data);

    // 加载单个质谱数据（内部辅助方法）
    bool loadMassData(int index, const std::vector<qint64> &indexArray, QByteArray &dataY, QByteArray &streamBody, const QString &filePath);

    // ========== 从MRMReader迁移的方法 ==========
    // 获取当前扫描类型（默认获取第一个Event的类型，兼容旧代码）
    GlobalEnums::ScanMode getCurrentScanMode(FileData &data);

    // 获取指定Event的扫描类型
    GlobalEnums::ScanMode getScanModeForEvent(FileData &data, int eventId);

    // 获取Segment中的Event数量
    int getEventCount(FileData &data);

    // 判断是否为MRM数据
    bool isMRMData(FileData &data);

    // 获取MRM列表数据
    QVector<StructMRM> getMRMDataList(FileData &data);

private:
    // ========== 原有私有方法 ==========
    //*测试代码，随时可删除 */////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////
    void qDebugStreamHead(_StreamHead *streamHead);
    void qDebugStreamHeadParam(cParamValue::_StreamHeadParam *param);
    void qDebugEvents(cParamValue::_Segment *pSegment);
    //*测试代码，随时可删除 */////////////⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️///////////////

    int updateXIC(const QString &XicString);
    QString getNumXIC(const QByteArray &pStreamHead);
    QString getNumOtherLine(const QByteArray &pStreamHead);
    int getNumOtherLine(const QString &str, QList<std::vector<double>> &pStructLines);
    // bool oldFileStruct = true; // 当前处于老文件结构，还没有segNo eventNo experimentNo，所以只能当作一个文件一个experiment

    template <typename T>
    static void fillData(const QByteArray &pSrcData, QByteArray &pDstData, int countACC, int nFrameB, int nFrameE);

    // ========== 从LxCustomDataReader迁移的私有方法 ==========
    // XIC参数结构体（从LxCustomDataReader迁移）
    struct XICParam
    {
        double mz;
        double tolerance;
        QString name;
        int eventId;
        std::vector<double> yListXIC; // XIC数据列表

        XICParam() : mz(0.0), tolerance(0.0), eventId(0) {}
        XICParam(double m, double t, const QString &n, int e) : mz(m), tolerance(t), name(n), eventId(e) {}
    };

    // 核心读取函数
    bool readFileHeader(const QString &filePath, QByteArray &streamHead);
    bool parseStreamHead(const QByteArray &streamHead, int &numXIC, int &numOtherLine, QMap<quint32, QMap<QString, XICParam *>> &xicMap);

    bool readAllTICData(const QString &filePath, const QByteArray &streamHead, int numXIC, int numOtherLine, std::vector<qint64> &indexArray,
                        std::vector<double> &ticX, std::vector<double> &ticY, QMap<quint32, QMap<QString, XICParam *>> &xicMap,
                        QList<std::vector<double>> &structLines);

    bool readMassDataBody(const QString &filePath, const std::vector<qint64> &indexArray, int frameBegin, int frameEnd, QByteArray &dataY,
                          QByteArray &streamBody);

    // 数据类型转换模板方法
    template <typename T>
    void convertDataType(const QByteArray &inputData, QByteArray &outputData, int accCount, int frameBegin, int frameEnd);

    // 解析质谱数据（旧版本，保持兼容性）
    bool parseMassData(const QByteArray &dataY, const QByteArray &streamBody, QVector<double> &massX, QVector<double> &massY, FileData *data, int eventId);

    // 🎯 新版本：禁止推算的质谱数据解析
    bool parseMassData(FileData *data, int eventId, const QByteArray &streamBody, QVector<double> &massX, QVector<double> &massY);

    // 🎯 新版本：包含Y轴数据的质谱数据解析
    bool parseMassDataWithYData(FileData *data, int eventId, const QByteArray &massData, const QByteArray &streamBody, QVector<double> &massX, QVector<double> &massY);

    // 🎯 MRM/SIM数据解析
    bool parseMRMSIMData(const _StreamBody *streamBodyPtr, const QByteArray &streamBody, QVector<double> &massX, QVector<double> &massY);

    // 🎯 FullScan数据解析
    bool parseFullScanData(const _StreamBody *streamBodyPtr, const QByteArray &streamBody, QVector<double> &massX, QVector<double> &massY);

    // 🎯 强度数据解析
    bool parseIntensityData(const _StreamBody *streamBodyPtr, const QByteArray &streamBody, QVector<double> &massY, int expectedCount);

    // 🎯 类型化强度数据解析模板
    template <typename T>
    bool parseTypedIntensityData(const char *dataPtr, int dataSize, QVector<double> &massY, int expectedCount, bool isCompressed);

    // 🎯 从Segment获取MRM真实m/z值
    bool getMRMRealMzValues(FileData *data, int eventId, QVector<double> &massX);

    // 🎯 提取不同类型MRM事件的m/z值
    bool extractMRMMzValues(const cParamValue::_EventMRM *pEventMRM, QVector<double> &massX);
    bool extractMRM2048MzValues(const cParamValue::_EventMRM2048 *pEventMRM, QVector<double> &massX);
    bool extractSIMMzValues(const cParamValue::_EventSIM *pEventSIM, QVector<double> &massX);

    // 从StreamBody中解析m/z范围
    QPair<double, double> parseMzRangeFromStreamBody(const QByteArray &streamBody);

    // 辅助函数
    QString extractXICString(const QByteArray &streamHead);
    QString extractOtherLineString(const QByteArray &streamHead);
    int parseXICString(const QString &xicString, QMap<quint32, QMap<QString, XICParam *>> &xicMap);
    int parseOtherLineString(const QString &lineString, QList<std::vector<double>> &structLines);

    // ========== 成员变量 ==========
    // MRM数据列表（从MRMReader迁移）
    QVector<StructMRM> Struct_MRM_Vec;

signals:
};

#endif // LXDATAREADER_H
