# 质谱数据文件读取源码实现详解

## 📋 **基于 LibDataFile 和 DataAnalysisHZH 的源码分析**

### **1. TIC 数据读取的具体实现步骤**

#### **1.1 入口函数：cDataFileRead::loadFileTIC()**

```cpp
bool cDataFileRead::loadFileTIC(double Period,
                               std::vector<qint64>& pIndexArray,
                               std::vector<double>& pTicX,
                               std::vector<double>& pTicY,
                               QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC,
                               QList<std::vector<double>>& pStructLines,
                               QByteArray& pStreamHead,
                               QVector<qint64>& pPageTIC,
                               QString pFilePathName)
{
    // 直接调用核心实现函数
    return loadDataHead(pFilePathName, Period, pIndexArray, pTicX, pTicY,
                       pXIC, pStructLines, pStreamHead, pPageTIC);
}
```

#### **1.2 核心实现：loadDataHead()**

**步骤 1：文件头读取**

```cpp
// 1. 打开Param文件
QFile tmpFile(pFilePath);
qint64 sizeHead = sizeof(_StreamHead);
pStreamHead.resize(sizeHead);

// 2. 读取基础头部
tmpFile.read(pStreamHead.data(), sizeHead);

// 3. 获取完整头部长度并重新读取
_StreamHead* tmpStreamHead = (_StreamHead*)(pStreamHead.data());
sizeHead = tmpStreamHead->length;
pStreamHead.resize(sizeHead);
tmpFile.seek(0);
tmpFile.read(pStreamHead.data(), sizeHead);
```

**步骤 2：解析 XIC 和其他线参数**

```cpp
// 4. 解析XIC参数数量
int numXIC = updateXIC(getNumXIC(pStreamHead), pXIC);

// 5. 解析其他数据线数量
int numOtherLine = getNumOtherLine(getNumOtherLine(pStreamHead), pStructLines);
```

**步骤 3：计算数据结构偏移量**

```cpp
// 6. 计算每行数据的结构
int sizeI64 = sizeof(qint64);    // 索引大小
int sizeD = sizeof(double);      // 数据大小

// 每行数据结构：[索引][TIC_X][TIC_Y][XIC数据...][其他线数据...]
int offsetI = sizeI64 + sizeD * 2 + sizeD * numXIC + sizeD * numOtherLine;  // 总行长度
int offsetY = sizeI64 + sizeD;                                               // TIC_Y偏移
int offsetXIC = sizeI64 + sizeD + sizeD;                                    // XIC数据偏移
int offsetOtherLine = offsetXIC + numXIC * sizeD;                           // 其他线偏移

qint64 lineData = sizeData / offsetI;  // 总行数
```

**步骤 4：分页处理判断**

```cpp
// 7. 计算时间间隔
QByteArray tempArray;
tempArray.resize(offsetI * 3);
tmpFile.read(tempArray.data(), offsetI * 3);

double interval = *((double*)(tempArray.data() + offsetI * 2 + sizeI64)) -
                  *((double*)(tempArray.data() + offsetI + sizeI64));
double allTime = ((double)sizeData) / ((double)offsetI) * interval;

// 8. 判断是否需要分页
if (allTime < Period) {
    // 单页处理
    pPageTIC.resize(1);
    pPageTIC[0] = sizeHead;
} else {
    // 多页处理
    qint64 sizeSpread = qint64(Period / interval) * offsetI;
    // 计算分页表...
}
```

**步骤 5：数据提取**

```cpp
// 9. 逐行提取数据
for (qint64 i = 0; i < lineData; ++i) {
    int offsetLine = i * offsetI;

    // 提取索引
    memcpy(pIndexArray.data() + i, tempArray.data() + offsetLine, sizeI64);

    // 提取TIC X轴（时间）
    memcpy(pTicX.data() + i, tempArray.data() + offsetLine + sizeI64, sizeD);

    // 提取TIC Y轴（强度）
    memcpy(pTicY.data() + i, tempArray.data() + offsetLine + offsetY, sizeD);

    // 提取XIC数据（如果存在）
    if (!pXIC.isEmpty()) {
        int j = 0;
        QMutableMapIterator<uint32_t, QMap<QString, _PARAM_XIC*>> eventIter(pXIC);
        while (eventIter.hasNext()) {
            QMutableMapIterator<QString, _PARAM_XIC*> massIter(eventIter.next().value());
            while (massIter.hasNext()) {
                _PARAM_XIC* pGRAPH_XIC = massIter.next().value();
                pGRAPH_XIC->yListXIC.resize(lineData);
                memcpy(pGRAPH_XIC->yListXIC.data() + i,
                       tempArray.data() + offsetLine + offsetXIC + sizeD * j, sizeD);
                ++j;
            }
        }
    }

    // 提取其他数据线
    for (int j = 0; j < numOtherLine; ++j) {
        memcpy(pStructLines[j].data() + i,
               tempArray.data() + offsetLine + offsetOtherLine + sizeD * j, sizeD);
    }
}
```

### **2. MASS 数据读取的具体实现步骤**

#### **2.1 入口函数：cDataFileRead::loadFileMass()**

```cpp
bool cDataFileRead::loadFileMass(int index,
                                const std::vector<qint64>& pIndexArray,
                                QByteArray& pData,
                                QByteArray& pStreamBody,
                                QString pFilePathName)
{
    // 调用核心实现，读取单帧数据
    return loadDataBody(pFilePathName, pIndexArray, index, index, pData, pStreamBody);
}
```

#### **2.2 核心实现：loadDataBody()**

**步骤 1：文件定位**

```cpp
// 1. 打开Dat文件
QFile tmpDataFileMass(pFilePath);
tmpDataFileMass.open(QIODevice::ReadOnly);

// 2. 根据索引定位到指定帧
qint64 offset = pIndexArray[nFrameB + tmpACC];  // 获取帧偏移量
tmpDataFileMass.seek(offset);                   // 定位到帧位置
```

**步骤 2：读取数据体头部**

```cpp
// 3. 读取StreamBody头部
qint64 sizeStreamBody = sizeof(_StreamBody);
pStreamBody.resize(sizeStreamBody);
tmpDataFileMass.read(pStreamBody.data(), sizeStreamBody);

// 4. 处理扩展参数（如果存在）
_StreamBody* tmpStreamBody = (_StreamBody*)(pStreamBody.data());
if (tmpStreamBody->lengthParam > 0) {
    ulong lengthParam = tmpStreamBody->lengthParam;
    pStreamBody.resize(sizeStreamBody + lengthParam);
    tmpDataFileMass.read(pStreamBody.data() + sizeStreamBody, lengthParam);
    tmpStreamBody = (_StreamBody*)(pStreamBody.data());
}
```

**步骤 3：读取和解压缩数据**

```cpp
// 5. 计算数据体大小
qint64 sizeBody = tmpStreamBody->length - sizeStreamBody - tmpStreamBody->lengthParam;

// 6. 读取原始数据
QByteArray tmpArray;
tmpArray.resize(sizeBody);
tmpDataFileMass.read(tmpArray.data(), sizeBody);

// 7. 根据数据类型进行解压缩和转换
QByteArray uncompressBuff;
switch (tmpStreamBody->typeParam) {
    case _StreamBody::Type_Uint16Compress:
        uncompressBuff = qUncompress(tmpArray);
        fillData<quint16>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
        break;
    case _StreamBody::Type_FloatCompress:
        uncompressBuff = qUncompress(tmpArray);
        fillData<float>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
        break;
    case _StreamBody::Type_DoubleCompress:
        uncompressBuff = qUncompress(tmpArray);
        fillData<double>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
        break;
    // ... 其他数据类型
}
```

**步骤 4：数据类型转换（fillData 模板函数）**

```cpp
template<typename T>
void cDataFileRead::fillData(const QByteArray& pSrcData,
                            QByteArray& pDstData,
                            int countACC, int nFrameB, int nFrameE)
{
    qint64 sizeData = pSrcData.size() / sizeof(T);
    const T* pSrcY = reinterpret_cast<const T*>(pSrcData.data());

    if (countACC == 0) {
        // 首次加载，直接转换为double
        pDstData.resize(sizeData * sizeof(double));
        double* pDstY = (double*)pDstData.data();
        for (int uIndex = 0; uIndex < sizeData; ++uIndex) {
            pDstY[uIndex] = (double)(pSrcY[uIndex]);
        }
    } else {
        // 累积平均（用于多帧平均）
        double* pDstY = (double*)pDstData.data();
        for (int uIndex = 0; uIndex < sizeData; ++uIndex) {
            pDstY[uIndex] = (double)(countACC-1) / (double)(countACC) * pDstY[uIndex] +
                           (double)(pSrcY[uIndex]) / (double)(countACC);
        }
    }
}
```

### **3. XIC 参数解析的具体实现**

#### **3.1 XIC 参数提取：getNumXIC()**

```cpp
QString cDataFileRead::getNumXIC(const QByteArray& pStreamHead)
{
    // 1. 解析StreamHead参数列表
    QList<cParamValue::_StreamHeadParam*> tmpList;
    _StreamHead::toList(pStreamHead, tmpList);

    // 2. 查找XIC参数
    for (int i = 0; i < tmpList.size(); ++i) {
        if (tmpList[i]->type == cParamValue::Type_XIC_Param) {
            // 3. 提取XIC字符串
            QByteArray tmpArray;
            tmpArray.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            QString XicString = QString::fromUtf8(tmpArray);
            return XicString;
        }
    }
    return QString();
}
```

#### **3.2 XIC 结构构建：updateXIC()**

```cpp
int cDataFileRead::updateXIC(const QString& XicString,
                            QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC)
{
    // 1. 解析XIC字符串格式：massRange/event1:mass1:color1:x1:y1@event2:mass2:color2:x2:y2@...
    QStringList lstStr = XicString.split('/');
    if (lstStr.size() < 2) return 0;

    // 2. 清空现有XIC数据
    foreach (auto tmpKey, pXIC.keys()) {
        foreach (auto tmpKey1, pXIC[tmpKey].keys()) {
            delete pXIC[tmpKey][tmpKey1];
        }
    }
    pXIC.clear();

    // 3. 解析质量范围
    double massRange = lstStr[0].toDouble();

    // 4. 解析每个XIC曲线
    QStringList lstCurves = lstStr[1].split('@');
    foreach (QString strCurve, lstCurves) {
        QStringList lstCurveInfo = strCurve.split(':');
        if (lstCurveInfo.size() < 5) continue;

        // 5. 创建XIC参数对象
        uint32_t tempEvent = lstCurveInfo[0].toInt();      // 事件ID
        QString tempMass = lstCurveInfo[1];                // 质量值
        uint32_t color = lstCurveInfo[2].toUInt(nullptr, 16); // 颜色
        double mass = lstCurveInfo[3].toDouble();          // 质量
        double intensity = lstCurveInfo[4].toDouble();     // 强度

        pXIC[tempEvent][tempMass] = new _PARAM_XIC(massRange, color, mass, intensity);
    }

    return lstCurves.size();
}
```

### **4. 数据文件结构总结**

#### **4.1 Param 文件结构**

```
[StreamHead]                    // 文件头
├── length                      // 头部总长度
├── dateTime                    // 时间戳
└── 参数块列表
    ├── Type_XIC_Param         // XIC参数
    ├── Type_Segment_Param     // 段参数
    ├── Type_Method_Param      // 方法参数
    └── ...

[Data Section]                  // 数据部分
├── 行1: [索引][TIC_X][TIC_Y][XIC1][XIC2]...[其他线1][其他线2]...
├── 行2: [索引][TIC_X][TIC_Y][XIC1][XIC2]...[其他线1][其他线2]...
└── ...
```

#### **4.2 Dat 文件结构**

```
[Frame1]
├── StreamBody                  // 数据体头部
│   ├── length                 // 数据长度
│   ├── typeParam             // 数据类型（压缩/未压缩）
│   └── lengthParam           // 扩展参数长度
├── 扩展参数（可选）
└── 质谱数据（可能压缩）

[Frame2]
├── StreamBody
├── 扩展参数
└── 质谱数据
...
```

### **5. XIC 数据生成的具体实现步骤**

#### **5.1 基于 DataAnalysisHZH 的 XIC 生成流程**

**步骤 1：数据分解（dataDisassembleFirst）**

```cpp
uint32_t dataDisassembleFirst(QByteArray& pByteArray,
                             cParamValue::_Segment* pSegment,
                             QList<std::vector<double>>& pListX,
                             QList<std::vector<double>>& pListY,
                             QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>>& pSTRUCT_DATA)
{
    // 1. 计算总数据点数
    uint32_t uAllPoint = pByteArray.size() / sizeof(double);
    double dbEvtTimeSum = cParamValue::_Segment::getSegTimeMs(pSegment);

    // 2. 初始化事件数据容器
    pSTRUCT_DATA.clear();
    for (uint32_t currentEvt = 0; currentEvt < pSegment->countsEvent; currentEvt++) {
        pSTRUCT_DATA.append(QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>());
    }

    // 3. 遍历所有事件
    int offsetP = 0;
    double* pdbOffset = (double*)(pByteArray.data());

    for (uint32_t currentEvt = 0; currentEvt < pSegment->countsEvent; ++currentEvt) {
        // 4. 获取事件指针
        cParamValue::_Event* pEvent = (cParamValue::_Event*)((char*)&(pSegment->fisrtEvent) + offsetP);

        // 5. 计算事件数据点数
        _CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA = &(pSTRUCT_DATA[currentEvt].second);
        tempSTRUCT_DATA->uEvtValidPoint = (uint32_t)(pEvent->holdTimeMs * uAllPoint / dbEvtTimeSum);

        // 6. 根据事件类型进行不同处理
        pSTRUCT_DATA[currentEvt].first = pEvent->type;

        if (cParamValue::Type_SIM == pEvent->type) {
            offsetP += sizeof(cParamValue::_EventSIM);
            disassembleSIM(tempSTRUCT_DATA, (cParamValue::_EventSIM*)pEvent,
                          pListX[currentEvt], pListY[currentEvt], pdbOffset);
        }
        else if (cParamValue::Type_Scan == pEvent->type) {
            offsetP += sizeof(cParamValue::_EventScan);
            disassembleScan(tempSTRUCT_DATA, (cParamValue::_EventScan*)pEvent,
                           pListX[currentEvt], pListY[currentEvt], pdbOffset);
        }
        // ... 其他事件类型

        // 7. 移动数据指针
        pdbOffset += tempSTRUCT_DATA->uEvtValidPoint;
    }
}
```

**步骤 2：SIM 事件数据分解**

```cpp
template<typename EventSIM>
bool disassembleSIM(_CONGIG_OMS::_STRUCT_DATA* pSTRUCT_DATA,
                   EventSIM* pEventSIM,
                   std::vector<double>& pX,
                   std::vector<double>& pY,
                   double* pdbOffset)
{
    // 1. 预分配内存
    if (!vectorOperate::Resize(pY, pSTRUCT_DATA->uEvtValidPoint) ||
        !vectorOperate::Resize(pX, pSTRUCT_DATA->uEvtValidPoint)) {
        return false;
    }

    // 2. 提取SIM数据
    const double* pFirst = pdbOffset + pSTRUCT_DATA->uDelayPoint;
    for (int indexM = 0; indexM < pSTRUCT_DATA->uEvtValidPoint; ++indexM) {
        if (pEventSIM->mass[indexM] < 0.0000001) break;

        // 3. 设置质量值和强度值
        pX[indexM] = pEventSIM->mass[indexM];    // 质量值
        pY[indexM] = *(pFirst + indexM);         // 强度值
    }

    return true;
}
```

**步骤 3：Scan 事件数据分解**

```cpp
bool disassembleScan(_CONGIG_OMS::_STRUCT_DATA* pSTRUCT_DATA,
                    cParamValue::_EventScan* pEventScan,
                    std::vector<double>& pX,
                    std::vector<double>& pY,
                    double* pdbOffset)
{
    // 1. 预分配内存
    if (!vectorOperate::Resize(pY, pSTRUCT_DATA->uEvtValidPoint) ||
        !vectorOperate::Resize(pX, pSTRUCT_DATA->uEvtValidPoint)) {
        return false;
    }

    // 2. 应用质量校准
    _FUNTION_OMS::calibrationF(mCALIBRATE, pX, pEventScan, pSTRUCT_DATA);

    // 3. 拷贝强度数据
    memcpy(pY.data(), pdbOffset, pSTRUCT_DATA->uEvtValidPoint * sizeof(double));

    return true;
}
```

**步骤 4：XIC 强度提取（基于 m/z 匹配）**

```cpp
bool extractIntensityForMz(const std::vector<double>& mzValues,
                          const std::vector<double>& intensities,
                          double targetMz,
                          double threshold,
                          double& extractedIntensity)
{
    extractedIntensity = 0.0;
    double sumMz = 0;
    int matchCount = 0;

    // 1. 在m/z范围内查找匹配值
    for (size_t i = 0; i < mzValues.size(); ++i) {
        double diff = qAbs(mzValues[i] - targetMz);
        if (diff <= threshold) {
            // 2. 累积匹配的强度值
            sumMz += intensities[i];
            matchCount++;
        }
    }

    extractedIntensity = sumMz;
    return matchCount > 0;
}
```

### **6. 完整的 XIC 生成工作流程**

#### **6.1 多文件 TIC 加载（loadFileThreadTIC）**

```cpp
int loadFileThreadTIC()
{
    int numFile = currentFileTIC().size();

    // 1. 循环处理每个文件
    for (int indexFile = 0; indexFile < numFile; ++indexFile) {
        std::vector<qint64> IndexArray;
        std::vector<double> DataTIC_X, DataTIC_Y;
        QByteArray StreamHead;
        QVector<qint64> PageTIC;

        // 2. 根据文件数量选择加载方式
        if (numFile == 1) {
            // 单文件：同时加载XIC数据
            QMap<uint32_t, QMap<QString, _PARAM_XIC*>> tempMap;
            cDataFileRead::loadFileTIC(getConfig()->Period,
                                      IndexArray, DataTIC_X, DataTIC_Y,
                                      tempMap,  // XIC映射
                                      DataOtherLines_Y, StreamHead, PageTIC,
                                      currentFileTIC()[indexFile]);
            updateXIC(tempMap);  // 更新XIC索引
        } else {
            // 多文件：不加载XIC数据
            cDataFileRead::loadFileTIC(getConfig()->Period,
                                      IndexArray, DataTIC_X, DataTIC_Y,
                                      DataOtherLines_Y, StreamHead, PageTIC,
                                      currentFileTIC()[indexFile]);
        }

        // 3. 解析头文件参数
        QByteArray segment, tuneFile;
        QString pPropertyStr;
        splitStreamHead(segment, StreamHead, pPropertyStr, tuneFile);

        // 4. 存储数据
        mIndexArray << IndexArray;
        mDataTIC_X << DataTIC_X;
        mDataTIC_Y << DataTIC_Y;
        mSegment << segment;
    }
}
```

#### **6.2 MASS 数据加载和 XIC 生成（loadFileThreadMass）**

```cpp
int loadFileThreadMass()
{
    // 1. 加载指定帧的MASS数据
    QByteArray tmpArray;
    cDataFileRead::loadFileMass(currentFrame()[indexFile],
                               mIndexArray[indexFile],
                               tmpArray,
                               mStreamBody[indexFile],
                               filePathD);

    // 2. 数据分解
    QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> mListSTRUCT_DATA;
    cParamValue::_Segment* pSegmentLIT = getSegment(indexFile);
    dataDisassembleFirst(tmpArray, pSegmentLIT,
                        tmpThreadBuffX[indexFile],
                        tmpThreadBuffY[indexFile],
                        mListSTRUCT_DATA);

    // 3. 分析每个事件
    uint32_t countEvt = mListSTRUCT_DATA.size();
    for (uint32_t currentEvt = 0; currentEvt < countEvt; currentEvt++) {
        _CONGIG_OMS::_STRUCT_DATA* pSTRUCT_DATA = &(mListSTRUCT_DATA[currentEvt].second);

        if (cParamValue::Type_SIM == mListSTRUCT_DATA[currentEvt].first) {
            // SIM数据分析
            AnalyzeSIM(tmpThreadBuffX[indexFile][currentEvt],
                      tmpThreadBuffY[indexFile][currentEvt],
                      *pSTRUCT_DATA, currentEvt, countEvt, tmpSTRUCT_PEAK);
        } else {
            // Scan数据分析
            AnalyzeScan(tmpThreadBuffX[indexFile][currentEvt].data(),
                       tmpThreadBuffY[indexFile][currentEvt].data(),
                       *pSTRUCT_DATA, currentEvt, countEvt, tmpSTRUCT_PEAK);
        }
    }
}
```

### **7. 数据流向总结**

```
[Param文件] → loadFileTIC() → loadDataHead() → 提取TIC数据和XIC参数
     ↓
[TIC时间轴] + [XIC配置信息]
     ↓
[Dat文件] → loadFileMass() → loadDataBody() → 提取原始质谱数据
     ↓
dataDisassembleFirst() → 按事件类型分解数据
     ↓
disassembleSIM() / disassembleScan() → 生成m/z和强度数组
     ↓
extractIntensityForMz() → 根据目标m/z提取强度
     ↓
[XIC曲线数据]
```

这个详细的源码分析展示了 LibDataFile 和 DataAnalysisHZH 中 TIC 读取、MASS 数据处理和 XIC 生成的完整实现过程，包括文件结构解析、数据提取、事件分解、类型转换和 XIC 参数处理等所有关键步骤。
