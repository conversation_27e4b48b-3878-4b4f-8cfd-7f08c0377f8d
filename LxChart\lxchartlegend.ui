<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LxChartLegend</class>
 <widget class="QWidget" name="LxChartLegend">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>902</width>
    <height>558</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QGridLayout" name="gridLayout" columnstretch="0,0,1">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QLabel" name="label_legend">
     <property name="minimumSize">
      <size>
       <width>10</width>
       <height>10</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>10</width>
       <height>10</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="0" column="2">
    <widget class="QPushButton" name="btn_name">
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QPushButton" name="btn_type">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="iconSize">
      <size>
       <width>30</width>
       <height>30</height>
      </size>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
