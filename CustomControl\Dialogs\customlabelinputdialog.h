#ifndef CUSTOMLABELINPUTDIALOG_H
#define CUSTOMLABELINPUTDIALOG_H

#include <QDialog>
#include <QFontDialog>
#include <QColorDialog>
#include <QDebug>
#include <tuple>

namespace Ui {
class CustomLabelInputDialog;
}

class CustomLabelInputDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CustomLabelInputDialog(QWidget *parent = nullptr);
    ~CustomLabelInputDialog();
    void connectAll();
    static std::tuple<QString, QFont, QColor> getContent();
signals:
    void contentReady();

private:
    QString content;
    QFont textFont;
    QColor textColor;

private:
    Ui::CustomLabelInputDialog *ui;
};

#endif // CUSTOMLABELINPUTDIALOG_H
