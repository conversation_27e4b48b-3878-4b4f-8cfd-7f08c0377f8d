<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1058</width>
    <height>818</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_3">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <property name="spacing">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <layout class="QGridLayout" name="gridLayout" rowstretch="0,1">
      <item row="0" column="0">
       <layout class="QGridLayout" name="gridLayout_top">
        <property name="leftMargin">
         <number>5</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="horizontalSpacing">
         <number>7</number>
        </property>
        <item row="0" column="4">
         <widget class="QPushButton" name="btn_show">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="cursor">
           <cursorShape>ArrowCursor</cursorShape>
          </property>
          <property name="text">
           <string>显示</string>
          </property>
         </widget>
        </item>
        <item row="0" column="6">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="5">
         <widget class="QPushButton" name="btn_process">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="cursor">
           <cursorShape>ArrowCursor</cursorShape>
          </property>
          <property name="text">
           <string>处理</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QPushButton" name="btn_options">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="cursor">
           <cursorShape>ArrowCursor</cursorShape>
          </property>
          <property name="text">
           <string>选项</string>
          </property>
          <property name="icon">
           <iconset>
            <normaloff>:/Icons/LxChart/Options.svg</normaloff>:/Icons/LxChart/Options.svg</iconset>
          </property>
          <property name="iconSize">
           <size>
            <width>30</width>
            <height>30</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QPushButton" name="btn_undo">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="cursor">
           <cursorShape>ArrowCursor</cursorShape>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="icon">
           <iconset>
            <normaloff>:/Icons/LxChart/Undo.svg</normaloff>:/Icons/LxChart/Undo.svg</iconset>
          </property>
          <property name="iconSize">
           <size>
            <width>40</width>
            <height>40</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QPushButton" name="btn_add">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="cursor">
           <cursorShape>ArrowCursor</cursorShape>
          </property>
          <property name="toolTip">
           <string>打开数据文件</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="icon">
           <iconset>
            <normaloff>:/Icons/LxChart/Folder.svg</normaloff>:/Icons/LxChart/Folder.svg</iconset>
          </property>
          <property name="iconSize">
           <size>
            <width>40</width>
            <height>40</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QPushButton" name="btn_redo">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="cursor">
           <cursorShape>ArrowCursor</cursorShape>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="icon">
           <iconset>
            <normaloff>:/Icons/LxChart/Redo.svg</normaloff>:/Icons/LxChart/Redo.svg</iconset>
          </property>
          <property name="iconSize">
           <size>
            <width>40</width>
            <height>40</height>
           </size>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="1" column="0">
       <widget class="CustomSplitter" name="splitter">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <widget class="QWidget" name="gridLayoutWidget">
         <layout class="QGridLayout" name="gridLayout_TICXIC"/>
        </widget>
        <widget class="QWidget" name="gridLayoutWidget_2">
         <layout class="QGridLayout" name="gridLayout_Mass"/>
        </widget>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1058</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CustomSplitter</class>
   <extends>QSplitter</extends>
   <header location="global">customsplitter.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
