#ifndef LXCHARTDATA_H
#define LXCHARTDATA_H

#include <QObject>
#include <QVector>
#include <QPointF>
#include <QString>
#include <QPair>
#include <QColor>
#include <QMutex>
#include "Globals/GlobalDefine.h"
#include <QDebug>
#include <QPen>
#include "lxchartlegend.h"
#include "Algorithm/PeakFind/wh_peakSearch.h"
#include "nanoflann.hpp"
#include <QtCharts/QLineSeries>
#include <QtCharts/QBarSeries>
#include <QtCharts/QBarSet>

QT_CHARTS_USE_NAMESPACE

#include <QtMath>
#include "FileData/taskmanager.h"
#include <QTime>
#include <QUuid>
#include <QLineF>
/**
 * @brief LxChartData类用于给LxChart控件提供数据
 */
class LxChartData : public QObject
{
    Q_OBJECT
public:
    static QMutex idMutex;             // 用于保护UniqueID的互斥锁
    static unsigned int LxChartDataId; // 用于生成唯一ID的静态成员变量

    /**
     * @brief 构造函数
     * @param parent 父对象 ParamPath 从属的FileData对象Param文件绝对路径
     */
    explicit LxChartData(QString ParamPath, GlobalEnums::TrackType trackType, GlobalEnums::IonMode ionMode = GlobalEnums::IonMode::NagativeIon,
                         GlobalEnums::ScanMode scanMode = GlobalEnums::ScanMode::MRM, QString dataProcess = "无处理", QString dataName = "默认数据名",
                         QString sampleName = "默认样本名", int eventNum = 0, QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~LxChartData();

    /**
     * @brief 获取图表数据
     * @return 图表数据点集合
     */
    QVector<QPointF> getData() const;

    // 线程安全的数据操作方法 - 替代缓存结构体的功能
    void setDataThreadSafe(const QVector<double> &xData, const QVector<double> &yData);
    void appendDataPointThreadSafe(double x, double y);
    void clearDataThreadSafe();
    void setRangeThreadSafe(double minX, double maxX, double minY, double maxY);
    void resetRangeThreadSafe();
    QPair<QPair<double, double>, QPair<double, double>> getRangeThreadSafe() const; // ((minX, maxX), (minY, maxY))

    // 获取线程安全的数据副本
    QVector<double> getXDataThreadSafe() const;
    QVector<double> getYDataThreadSafe() const;
    QVector<QPointF> getDataThreadSafe() const;

    // 范围访问方法 - 替代直接访问私有成员
    double getMinX() const;
    double getMaxX() const;
    double getMinY() const;
    double getMaxY() const;
    bool hasValidRange() const; // 检查范围是否有效

    /**
     * @brief 设置图表数据（通用方法）
     * @param data 图表数据点集合
     */
    void setData(const QVector<QPointF> &data);

    /**
     * @brief 设置图表数据和范围
     * @param data 图表数据点集合
     * @param minX X轴最小值
     * @param maxX X轴最大值
     * @param minY Y轴最小值
     * @param maxY Y轴最大值
     */
    void setDataWithRange(const QVector<QPointF> &data, double minX, double maxX, double minY, double maxY);

    /**
     * @brief 设置原始数据x
     * @param x
     */
    void setDataX(const QVector<double> x);

    /**
     * @brief 设置原始数据y
     * @param y
     */
    void setDataY(const QVector<double> y);

    /**
     * @brief 获取原始数据x
     * @return
     */
    const QVector<double> getDataX();

    /**
     * @brief 获取原始数据y
     * @return
     */
    const QVector<double> getDataY();

    /**
     * @brief 获取图表标题
     * @return 图表标题
     */
    QString getTitle() const;

    /**
     * @brief 设置图表标题
     * @param title 图表标题
     */
    void setTitle(const QString &title);

    /**
     * @brief 获取X轴标签
     * @return X轴标签
     */
    QString getXAxisLabel() const;

    /**
     * @brief 设置X轴标签
     * @param label X轴标签
     */
    void setXAxisLabel(const QString &label);

    /**
     * @brief 获取X轴最小值
     * @return X轴最小值
     */
    double getXAxisMin() const;

    /**
     * @brief 设置X轴最小值
     * @param min X轴最小值
     */
    void setXAxisMin(double min);

    /**
     * @brief 获取X轴最大值
     * @return X轴最大值
     */
    double getXAxisMax() const;

    /**
     * @brief 设置X轴最大值
     * @param max X轴最大值
     */
    void setXAxisMax(double max);

    /**
     * @brief 设置X轴范围
     * @param min X轴最小值
     * @param max X轴最大值
     */
    void setXAxisRange(double min, double max);

    /**
     * @brief 设置Y轴范围
     * @param min Y轴最小值
     * @param max Y轴最大值
     */
    void setYAxisRange(double min, double max);

    /**
     * @brief 获取QAbstractSeries指针（可能是QLineSeries或QBarSeries）
     * @return QAbstractSeries指针
     */
    QAbstractSeries *getSeries() const { return m_series; }

    /**
     * @brief 设置QAbstractSeries指针
     * @param series QAbstractSeries指针
     */
    void setSeries(QAbstractSeries *series) { m_series = series; }

    /**
     * @brief 🎯 获取MRM/SIM多系列容器（每个棒子一个系列）
     * @return 多系列容器
     */
    const QVector<QAbstractSeries *> &getMrmBarSeries() const { return m_mrmBarSeries; }

    /**
     * @brief 🎯 添加MRM/SIM棒子系列
     * @param series 棒子系列
     */
    void addMrmBarSeries(QAbstractSeries *series) { m_mrmBarSeries.append(series); }

    /**
     * @brief 🎯 清除所有MRM/SIM棒子系列
     */
    void clearMrmBarSeries();

    /**
     * @brief 🎯 统一清理所有系列（包括主系列和MRM棒子系列）
     */
    virtual void clearAllSeries();

    /**
     * @brief 🎯 获取所有系列（包括主系列和MRM棒子系列）
     */
    QList<QAbstractSeries *> getAllSeries() const;

    /**
     * @brief 创建并设置新的图表系列（根据扫描模式自动选择QLineSeries或QBarSeries）
     * @param parent 父对象
     * @param name 系列名称
     * @return 创建的QAbstractSeries指针
     */
    QAbstractSeries *createSeries(QObject *parent = nullptr, const QString &name = "");

    /**
     * @brief 安全地删除图表系列
     */
    void deleteSeries();

    /**
     * @brief 判断当前数据是否应该使用棒状图显示
     * @return true表示使用棒状图，false表示使用连线图
     */
    bool shouldUseBarChart() const;

    /**
     * @brief 获取Y轴标签
     * @return Y轴标签
     */
    QString getYAxisLabel() const;

    /**
     * @brief 设置Y轴标签
     * @param label Y轴标签
     */
    void setYAxisLabel(const QString &label);

    /**
     * @brief 设置扫描模式
     * @param mode 扫描模式
     */
    void setScanMode(GlobalEnums::ScanMode mode);

    /**
     * @brief 获取扫描模式
     * @return 扫描模式
     */
    GlobalEnums::ScanMode getScanMode() const { return scanMode; }

    /**
     * @brief 获取背景区域
     * @return 背景区域（起点，终点）
     */
    QPair<double, double> getBackgroundArea() const;

    /**
     * @brief 设置背景区域
     * @param area 背景区域（起点，终点）
     */
    void setBackgroundArea(const QPair<double, double> &area);

    /**
     * @brief 获取标峰点
     * @return 标峰点集合
     */
    QVector<QPointF> getPeaks() const;

    /**
     * @brief 设置标峰点
     * @param peaks 标峰点集合
     */
    void setPeaks(const QVector<QPointF> &peaks);

    /**
     * @brief 获取曲线颜色
     * @return 曲线颜色
     */
    QColor getLineColor() const;

    /**
     * @brief 设置曲线颜色
     * @param color 曲线颜色
     */
    void setLineColor(const QColor &color);

    /**
     * @brief 获取曲线的原始颜色（用于高亮后恢复）
     * @return 原始曲线颜色
     */
    QColor getOriginalColor() const;

    /**
     * @brief 设置曲线的原始颜色（用于高亮后恢复）
     * @param color 原始曲线颜色
     */
    void setOriginalColor(const QColor &color);

    /**
     * @brief 获取从属的FileData的绝对路径（方便快速查找）
     * @return 从属的FileData的绝对路径
     */
    QString getParamPath();

    QString getUniqueID() const;

    // Legend ID管理
    QUuid getLegendId() const;
    void setLegendId(const QUuid &legendId);
    bool hasLegendId() const;

    /**
     * @brief 返回轨迹类型
     * @return
     */
    GlobalEnums::TrackType getTrackType();
    QPair<double, QPointF> getNearestPoint(QPointF point);

private:
    /**
     * @brief 初始化字符串参数
     */
    void initQStringParams();

public:
    LxChartLegend *legend = nullptr;
    bool m_bool_kdTreeEnabled = false;
    std::vector<Peak> peakVec;

    QPen getDefaultPen() const;
    void setDefaultPen(const QPen &newDefaultPen);

    int getEventNum() const;
    void setEventNum(int newEventNum);

    bool getHasFindPeak() const;
    void setHasFindPeak(bool newHasFindPeak);

    std::vector<Peak> getPeakVec() const;
    void setPeakVec(const std::vector<Peak> &newPeakVec);

    // MRM数据相关方法
    QMap<int, qreal> getMrmPositionToMzMap() const;
    void setMrmPositionToMzMap(const QMap<int, qreal> &map);
    qreal getMzValueForPosition(int position) const;

    // MRM柱子宽度设置
    void setBarWidth(qreal width) { m_barWidth = width; }
    qreal getBarWidth() const { return m_barWidth; }

    // MRM类别标签
    QStringList getMrmCategories() const { return m_mrmCategories; }
    bool hasMrmCategories() const { return !m_mrmCategories.isEmpty(); }

    // 🎯 新增：MASS分组位置相关方法
    int getMassStartPosition() const { return m_massStartPosition; }
    void setMassStartPosition(int position) { m_massStartPosition = position; }
    int getMassBarCount() const { return m_massBarCount; }
    void setMassBarCount(int count) { m_massBarCount = count; }

    // 选择性监测数据检测（用于垂直线条显示）- 包括MRM和SIM数据
    bool isSelectiveMonitoringData() const
    {
        return (trackType == GlobalEnums::TrackType::MS &&
                (scanMode == GlobalEnums::ScanMode::MRM || scanMode == GlobalEnums::ScanMode::SIM));
    }

    // 保持向后兼容的别名
    bool isMrmData() const { return isSelectiveMonitoringData(); }

private:
    void initKdTree(const QVector<QPointF> &vecPoint, double maxX, double minX, double maxY, double minY);
    GlobalDefine::PointCloud cloud;
    QMutex kdTreeMutex;
    using my_kd_tree_t = nanoflann::KDTreeSingleIndexAdaptor<nanoflann::L2_Simple_Adaptor<double, GlobalDefine::PointCloud>, GlobalDefine::PointCloud, 2>;
    my_kd_tree_t *kdtree = nullptr; // 使用指针存储 KD-Tree

    // QAbstractSeries指针，用于直接管理图表中的系列对象（可能是QLineSeries或QBarSeries）
    QAbstractSeries *m_series = nullptr;

    // 🎯 MRM/SIM数据的多系列容器（每个棒子一个系列）
    QVector<QAbstractSeries *> m_mrmBarSeries;

    // MRM数据的位置到m/z值的映射（位置1对应真实m/z值）
    QMap<int, qreal> m_mrmPositionToMzMap;

    // MRM柱子宽度设置
    qreal m_barWidth = 0.05; // MRM柱子宽度，默认为0.6（更细一些）

    // MRM类别标签
    QStringList m_mrmCategories; // 存储MRM数据的m/z值作为类别标签

    // 🎯 新增：MASS分组显示位置信息
    int m_massStartPosition = 1; // 当前MASS在X轴上的起始位置
    int m_massBarCount = 0;      // 当前MASS包含的棒子数量

protected:
    // 子类需要访问的成员变量
    QString m_qstr_paramPath;       /// 从属FileData的唯一路径
    GlobalEnums::IonMode ionMode;   // 离子模式
    GlobalEnums::ScanMode scanMode; // 扫描模式
    QString dataProcess;            // 数据处理
    QString dataName;               // 数据名
    QString sampleName;             // 样品名

private:
    // 默认命名规则参数
    GlobalEnums::TrackType trackType; // 轨迹模式
    int eventNum;                     // 事件号
    QString UniqueID;                 // 唯一ID
    QUuid m_legendId;                 // 关联的LxChartLegend的ID

    // 线程安全的数据存储 - 支持连续和离散数据
    mutable QMutex m_dataMutex;

    // 连续数据存储（用于TIC/XIC等连续曲线）
    QVector<double> m_xData; // 替代data_x，直接存储X数据
    QVector<double> m_yData; // 替代data_y，直接存储Y数据

    // 离散数据存储（用于MRM MASS等离散点数据）
    QVector<QPair<double, double>> m_discreteData; // 存储离散的(m/z, intensity)对

    // 兼容性接口
    QVector<QPointF> m_data; // 保持兼容性，从连续或离散数据生成

    // 数据范围信息 - 替代结构体中的范围信息
    double m_maxX = -1, m_minX = -1, m_maxY = -1, m_minY = -1;

    QString m_qstr_title;      ///< 图表标题
    QString m_qstr_xAxisLabel; ///< x轴标签
    QString m_qstr_yAxisLabel; ///< y轴标签

    QPair<double, double> m_qpair_backgroundArea; ///< 背景区域
    QVector<QPointF> m_qvector_peaks;             ///< 标峰点
    QColor m_qcolor_lineColor;                    ///< 曲线颜色
    QColor m_qcolor_originalColor;                ///< 原始曲线颜色（用于高亮后恢复）

    QPen defaultPen;

    bool hasFindPeak = false;
};

#endif // LXCHARTDATA_H
