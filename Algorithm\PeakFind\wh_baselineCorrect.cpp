#pragma once
#include <Eigen/Dense>
#include <Eigen/Sparse>  // 稀疏矩阵

// 三次多项式拟合基线校正
//y=ax^3 + bx^2 + cx + d
std::vector<double> cubicBaselineCorrection(const std::vector<double>& signal)
{
    int n = signal.size();
    Eigen::VectorXd y(n);
    for (int i = 0; i < n; ++i)
    {
        y(i) = signal[i];
    }

    // 构造设计矩阵 (1, x, x², x³)
    Eigen::MatrixXd X(n, 4);
    for (int i = 0; i < n; ++i)
    {
        double x = i;
        X(i, 0) = 1;      // 常数项
        X(i, 1) = x;       // 线性项
        X(i, 2) = x * x;   // 二次项
        X(i, 3) = x * x * x; // 三次项
    }

    // 最小二乘拟合
    Eigen::VectorXd coeffs = X.householderQr().solve(y);

    // 计算基线并校正
    std::vector<double> corrected(n);
    for (int i = 0; i < n; ++i)
    {
        double baseline = coeffs(0) + coeffs(1) * i + coeffs(2) * i * i + coeffs(3) * i * i * i;
        corrected[i] = signal[i] - baseline; //从原始数据中减去拟合结果，实现基线校正
    }

    return corrected;
}

// 静态分段拟合（每段固定点数）
//基于三次多项式拟合基线校正
//****************未完成**********************************//
std::vector<double> segmentedBaselineCorrection(const std::vector<double>& signal, int segmentSize)
{
    std::vector<double> corrected(signal.size());
    for (int i = 0; i < signal.size(); i += segmentSize)
    {
        int end = std::min(i + segmentSize, (int)signal.size());
        std::vector<double> segment(signal.begin() + i, signal.begin() + end);
        std::vector<double> segmentCorrected = cubicBaselineCorrection(segment);
        std::copy(segmentCorrected.begin(), segmentCorrected.end(), corrected.begin() + i);
    }
    return corrected;
}

/// <summary>
/// 非对称最小二乘（ALS）基线校正
/// </summary>
/// <param name="signal">原信号数据向量</param>
/// <param name="lambda">控制平滑强度（越大基线越平滑）	1e3 到 1e7</param>
/// <param name="p">非对称权重（越小越保护峰值）	0.001 到 0.1</param>
/// <param name="maxIter">迭代次数（通常 5-10 次收敛） 10 </param>
/// <returns>减掉基线后，所得数据向量</returns>
std::vector<double> alsBaselineCorrection(
    const std::vector<double>& signal,    double lambda,    double p,    int maxIter)
{
    int n = signal.size();
    Eigen::VectorXd y(n);
    for (int i = 0; i < n; ++i)
    {
        y(i) = signal[i];
    }

    // 构造二阶差分矩阵 D (n-2 x n)
    typedef Eigen::SparseMatrix<double> SpMat;
    typedef Eigen::Triplet<double> T;
    std::vector<T> tripletsD;
    for (int i = 0; i < n - 2; ++i)
    {
        tripletsD.push_back(T(i, i, 1));
        tripletsD.push_back(T(i, i + 1, -2));
        tripletsD.push_back(T(i, i + 2, 1));
    }
    SpMat D(n - 2, n);
    D.setFromTriplets(tripletsD.begin(), tripletsD.end());

    // ALS 迭代
    Eigen::VectorXd w(n), z = y;
    SpMat W(n, n);
    for (int iter = 0; iter < maxIter; ++iter)
    {
        w = (y - z).array().abs().pow(p - 1);

        // 构造对角权重矩阵 W
        std::vector<T> tripletsW;
        tripletsW.reserve(n);
        for (int i = 0; i < n; ++i)
        {
            tripletsW.push_back(T(i, i, w(i)));
        }
        W.setFromTriplets(tripletsW.begin(), tripletsW.end());

        SpMat A = W + lambda * D.transpose() * D;
        Eigen::SimplicialLDLT<SpMat> solver(A);
        z = solver.solve(W * y);
    }

    // 校正信号
    std::vector<double> corrected(n);
    for (int i = 0; i < n; ++i) corrected[i] = signal[i] - z(i);
    return corrected;
}

