# 质谱数据文件结构详解

## 📋 **概述**

本文档详细描述了质谱数据存储的两个核心文件：Param 文件（.P）和 Dat 文件（.D）的内部结构。基于 LibDataFile 源码分析得出。

## 🗂️ **Param 文件（.P）结构**

### **整体布局**

```
┌─────────────────────────────────────────────────────────────┐
│                        Param文件                            │
├─────────────────────────────────────────────────────────────┤
│                      StreamHead                             │
│                     (可变长度)                              │
├─────────────────────────────────────────────────────────────┤
│                    Data Section                             │
│                   (行式数据存储)                             │
└─────────────────────────────────────────────────────────────┘
```

### **StreamHead 详细结构**

```
StreamHead结构:
┌─────────────────────────────────────────────────────────────┐
│ _StreamHead (基础头部)                                       │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┬─────────────────┬─────────────────────┐ │
│ │ length (4字节)  │ dateTime (4字节)│ 其他基础字段...     │ │
│ └─────────────────┴─────────────────┴─────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 参数块列表 (可变数量)                                        │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ _StreamHeadParam #1                                     │ │
│ │ ┌─────────────┬─────────────┬─────────────────────────┐ │ │
│ │ │ type (4字节)│length (4字节)│ param[] (可变长度)      │ │ │
│ │ └─────────────┴─────────────┴─────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ _StreamHeadParam #2                                     │ │
│ │ ┌─────────────┬─────────────┬─────────────────────────┐ │ │
│ │ │ type (4字节)│length (4字节)│ param[] (可变长度)      │ │ │
│ │ └─────────────┴─────────────┴─────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ...                                                         │
└─────────────────────────────────────────────────────────────┘
```

### **参数块类型详解**

```
参数块类型枚举:
┌─────────────────────────────────────────────────────────────┐
│ Type_XIC_Param (XIC配置参数)                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 格式: "massRange/event1:mass1:color1:x1:y1@event2..."  │ │
│ │ 示例: "0.5/0:100.5:ff0000:100.5:1000.0@0:200.3:..."   │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Type_Segment_Param (段参数)                                 │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 包含事件定义、时间信息、采集参数等                       │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Type_Method_Param (方法参数)                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 包含仪器设置、采集方法等信息                             │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Type_Property_Str (属性字符串)                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 格式: "key1&value1;key2&value2;..."                    │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Type_Line_Param (其他数据线参数)                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 定义额外的数据线配置                                     │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **Data Section 结构**

```
Data Section (行式存储):
┌─────────────────────────────────────────────────────────────┐
│ 每行数据结构 (固定长度)                                      │
├─────────────────────────────────────────────────────────────┤
│ ┌───────┬───────┬───────┬─────────┬─────────┬─────────────┐ │
│ │ 帧索引│TIC_X  │TIC_Y  │XIC数据  │XIC数据  │其他线数据   │ │
│ │(8字节)│(8字节)│(8字节)│ #1     │ #2     │...          │ │
│ │       │       │       │(8字节) │(8字节) │             │ │
│ └───────┴───────┴───────┴─────────┴─────────┴─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 行长度计算公式:                                              │
│ offsetI = 8 + 8*2 + 8*numXIC + 8*numOtherLine              │
│        = 24 + 8*(numXIC + numOtherLine)                    │
├─────────────────────────────────────────────────────────────┤
│ 偏移量计算:                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ offsetY = 16 (TIC_Y偏移)                               │ │
│ │ offsetXIC = 24 (XIC数据起始偏移)                       │ │
│ │ offsetOtherLine = 24 + 8*numXIC (其他线数据起始偏移)   │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **数据提取示例**

```
数据提取过程:
┌─────────────────────────────────────────────────────────────┐
│ for (qint64 i = 0; i < lineData; ++i) {                    │
│     int offsetLine = i * offsetI;                          │
│                                                             │
│     ┌─────────────────────────────────────────────────────┐ │
│     │ // 提取帧索引                                       │ │
│     │ memcpy(pIndexArray.data() + i,                     │ │
│     │        tempArray.data() + offsetLine, 8);          │ │
│     └─────────────────────────────────────────────────────┘ │
│                                                             │
│     ┌─────────────────────────────────────────────────────┐ │
│     │ // 提取TIC时间                                      │ │
│     │ memcpy(pTicX.data() + i,                           │ │
│     │        tempArray.data() + offsetLine + 8, 8);      │ │
│     └─────────────────────────────────────────────────────┘ │
│                                                             │
│     ┌─────────────────────────────────────────────────────┐ │
│     │ // 提取TIC强度                                      │ │
│     │ memcpy(pTicY.data() + i,                           │ │
│     │        tempArray.data() + offsetLine + 16, 8);     │ │
│     └─────────────────────────────────────────────────────┘ │
│                                                             │
│     ┌─────────────────────────────────────────────────────┐ │
│     │ // 提取XIC数据                                      │ │
│     │ for (int j = 0; j < numXIC; ++j) {                 │ │
│     │     memcpy(xicData[j] + i,                         │ │
│     │            tempArray.data() + offsetLine + 24 +   │ │
│     │            j * 8, 8);                              │ │
│     │ }                                                   │ │
│     └─────────────────────────────────────────────────────┘ │
│ }                                                           │
└─────────────────────────────────────────────────────────────┘
```

## 🗂️ **Dat 文件（.D）结构**

### **整体布局**

```
┌─────────────────────────────────────────────────────────────┐
│                        Dat文件                              │
├─────────────────────────────────────────────────────────────┤
│                       Frame #1                             │
├─────────────────────────────────────────────────────────────┤
│                       Frame #2                             │
├─────────────────────────────────────────────────────────────┤
│                       Frame #3                             │
├─────────────────────────────────────────────────────────────┤
│                        ...                                  │
├─────────────────────────────────────────────────────────────┤
│                       Frame #N                             │
└─────────────────────────────────────────────────────────────┘
```

### **单帧详细结构**

```
Frame结构:
┌─────────────────────────────────────────────────────────────┐
│ _StreamBody (帧头部)                                        │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ┌─────────────┬─────────────┬─────────────┬───────────┐ │ │
│ │ │length       │lengthParam  │typeParam    │其他字段   │ │ │
│ │ │(4字节)      │(4字节)      │(4字节)      │...        │ │ │
│ │ │帧总长度     │扩展参数长度 │数据类型     │           │ │ │
│ │ └─────────────┴─────────────┴─────────────┴───────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 扩展参数 (可选，长度由lengthParam决定)                       │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ char extendedParams[lengthParam];                       │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 质谱数据 (可能压缩)                                          │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 数据长度 = length - sizeof(_StreamBody) - lengthParam  │ │
│ │                                                         │ │
│ │ 根据typeParam确定数据格式:                              │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ Type_Uint16Compress: 压缩的16位无符号整数           │ │ │
│ │ │ Type_FloatCompress:  压缩的32位浮点数               │ │ │
│ │ │ Type_DoubleCompress: 压缩的64位浮点数               │ │ │
│ │ │ Type_Uint16:         未压缩的16位无符号整数         │ │ │
│ │ │ Type_Float:          未压缩的32位浮点数             │ │ │
│ │ │ Type_Double:         未压缩的64位浮点数             │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **数据类型枚举**

```
typeParam数据类型:
┌─────────────────────────────────────────────────────────────┐
│ ┌─────────────────┬─────────────────┬─────────────────────┐ │
│ │ 类型值          │ 类型名称        │ 描述                │ │
│ ├─────────────────┼─────────────────┼─────────────────────┤ │
│ │ 0               │ Type_Uint8      │ 8位无符号整数       │ │
│ │ 1               │ Type_Uint8Compress│ 8位无符号整数(压缩)│ │
│ │ 2               │ Type_Uint16     │ 16位无符号整数      │ │
│ │ 3               │ Type_Uint16Compress│16位无符号整数(压缩)│ │
│ │ 4               │ Type_Uint32     │ 32位无符号整数      │ │
│ │ 5               │ Type_Uint32Compress│32位无符号整数(压缩)│ │
│ │ 6               │ Type_Float      │ 32位浮点数          │ │
│ │ 7               │ Type_FloatCompress│ 32位浮点数(压缩)   │ │
│ │ 8               │ Type_Double     │ 64位浮点数          │ │
│ │ 9               │ Type_DoubleCompress│64位浮点数(压缩)   │ │
│ └─────────────────┴─────────────────┴─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **帧读取流程**

```
帧读取过程:
┌─────────────────────────────────────────────────────────────┐
│ 1. 根据帧索引定位                                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ qint64 offset = pIndexArray[frameIndex];               │ │
│ │ tmpDataFileMass.seek(offset);                          │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 2. 读取StreamBody头部                                       │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ qint64 sizeStreamBody = sizeof(_StreamBody);           │ │
│ │ tmpDataFileMass.read(pStreamBody.data(),               │ │
│ │                      sizeStreamBody);                  │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 3. 读取扩展参数(如果存在)                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ if (tmpStreamBody->lengthParam > 0) {                 │ │
│ │     tmpDataFileMass.read(extParams,                    │ │
│ │                          lengthParam);                │ │
│ │ }                                                       │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 4. 读取质谱数据                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ qint64 sizeBody = tmpStreamBody->length -              │ │
│ │                   sizeStreamBody -                     │ │
│ │                   tmpStreamBody->lengthParam;          │ │
│ │ tmpDataFileMass.read(massData, sizeBody);              │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 5. 数据解压缩和类型转换                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ switch (tmpStreamBody->typeParam) {                    │ │
│ │     case Type_Uint16Compress:                          │ │
│ │         uncompressed = qUncompress(massData);          │ │
│ │         fillData<quint16>(uncompressed, output);       │ │
│ │         break;                                          │ │
│ │     // ... 其他类型处理                                │ │
│ │ }                                                       │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔗 **文件关联机制**

### **索引关联图**

```
Param文件与Dat文件的关联:
┌─────────────────────────────────────────────────────────────┐
│                    Param文件                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 行1: [索引1][时间1][强度1][XIC...][其他...]             │ │
│ │ 行2: [索引2][时间2][强度2][XIC...][其他...]             │ │
│ │ 行3: [索引3][时间3][强度3][XIC...][其他...]             │ │
│ │ ...                                                     │ │
│ └─────────────────────────────────────────────────────────┘ │
│                           │                                 │
│                           │ 索引映射                        │
│                           ▼                                 │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                    Dat文件                              │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ 偏移量=索引1: Frame1 (时间1的完整质谱数据)          │ │ │
│ │ │ 偏移量=索引2: Frame2 (时间2的完整质谱数据)          │ │ │
│ │ │ 偏移量=索引3: Frame3 (时间3的完整质谱数据)          │ │ │
│ │ │ ...                                                 │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📊 **实际数据示例**

### **Param 文件数据示例**

```
实际Param文件内容示例:
┌─────────────────────────────────────────────────────────────┐
│ StreamHead部分:                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ length: 2048 (头部总长度)                               │ │
│ │ dateTime: 1640995200 (2022-01-01 00:00:00)             │ │
│ │                                                         │ │
│ │ XIC参数: "0.5/0:100.5:ff0000:100.5:1000.0@             │ │
│ │               0:200.3:00ff00:200.3:2000.0"              │ │
│ │ 解析结果:                                               │ │
│ │   - 质量范围: ±0.5 Da                                  │ │
│ │   - XIC1: 事件0, m/z=100.5, 红色显示                  │ │
│ │   - XIC2: 事件0, m/z=200.3, 绿色显示                  │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Data Section部分 (假设有2个XIC，1个其他线):                 │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 行长度 = 8 + 8*2 + 8*2 + 8*1 = 48字节                  │ │
│ │                                                         │ │
│ │ 行1: [2048][0.0][1500.0][800.0][1200.0][25.6]         │ │
│ │      帧索引 时间  TIC强度  XIC1   XIC2   其他线         │ │
│ │                                                         │ │
│ │ 行2: [3200][0.1][1600.0][850.0][1250.0][26.1]         │ │
│ │ 行3: [4352][0.2][1700.0][900.0][1300.0][26.6]         │ │
│ │ ...                                                     │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **Dat 文件数据示例**

```
实际Dat文件内容示例:
┌─────────────────────────────────────────────────────────────┐
│ Frame1 (偏移量=2048):                                       │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ _StreamBody:                                            │ │
│ │   length: 8192 (整个帧长度)                             │ │
│ │   lengthParam: 0 (无扩展参数)                           │ │
│ │   typeParam: 3 (Type_Uint16Compress)                   │ │
│ │                                                         │ │
│ │ 质谱数据 (压缩后):                                      │ │
│ │   原始大小: 16000字节 (8000个uint16值)                  │ │
│ │   压缩后: 6144字节                                      │ │
│ │   解压后数据: [1500, 1520, 1480, 1600, ...]            │ │
│ │   对应m/z: [50.0, 50.1, 50.2, 50.3, ...]              │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Frame2 (偏移量=3200):                                       │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ _StreamBody:                                            │ │
│ │   length: 8256 (整个帧长度)                             │ │
│ │   lengthParam: 64 (有扩展参数)                          │ │
│ │   typeParam: 7 (Type_FloatCompress)                    │ │
│ │                                                         │ │
│ │ 扩展参数 (64字节):                                      │ │
│ │   温度: 23.5°C, 压力: 1.2e-6 Torr, ...                │ │
│ │                                                         │ │
│ │ 质谱数据 (压缩后):                                      │ │
│ │   原始大小: 16000字节 (4000个float值)                   │ │
│ │   压缩后: 6144字节                                      │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 **数据处理流程图**

### **完整的数据流向**

```mermaid
graph TD
    A[Param文件] --> B[读取StreamHead]
    B --> C[解析XIC参数]
    B --> D[解析Segment信息]
    C --> E[构建XIC映射表]
    D --> F[获取事件定义]

    A --> G[读取Data Section]
    G --> H[提取帧索引数组]
    G --> I[提取TIC时间轴]
    G --> J[提取TIC强度]
    G --> K[提取XIC数据]

    H --> L[Dat文件定位]
    L --> M[读取Frame头部]
    M --> N[读取扩展参数]
    M --> O[读取质谱数据]
    O --> P[数据解压缩]
    P --> Q[类型转换]

    F --> R[事件数据分解]
    Q --> R
    R --> S[SIM事件处理]
    R --> T[Scan事件处理]
    R --> U[MRM事件处理]

    S --> V[生成m/z数组]
    T --> V
    U --> V
    V --> W[生成强度数组]
    W --> X[显示质谱图]
```

### **内存布局示意图**

```
内存中的数据组织:
┌─────────────────────────────────────────────────────────────┐
│ FileData对象                                                │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ indexArray: [2048, 3200, 4352, 5504, ...]              │ │
│ │ dataTIC_X:  [0.0, 0.1, 0.2, 0.3, ...]                  │ │
│ │ dataTIC_Y:  [1500.0, 1600.0, 1700.0, ...]              │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ xicMap:                                                 │ │
│ │   Event0:                                               │ │
│ │     "100.5" -> _PARAM_XIC(range=0.5, color=0xff0000)   │ │
│ │     "200.3" -> _PARAM_XIC(range=0.5, color=0x00ff00)   │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ tmpThreadBuffX[0][0]: [50.0, 50.1, 50.2, ...]          │ │
│ │ tmpThreadBuffY[0][0]: [1500, 1520, 1480, ...]          │ │
│ │ (事件0的m/z和强度数据)                                  │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📏 **字节对齐和性能考虑**

### **数据对齐规则**

```
字节对齐要求:
┌─────────────────────────────────────────────────────────────┐
│ ┌─────────────────┬─────────────────┬─────────────────────┐ │
│ │ 数据类型        │ 大小(字节)      │ 对齐要求            │ │
│ ├─────────────────┼─────────────────┼─────────────────────┤ │
│ │ qint64          │ 8               │ 8字节对齐           │ │
│ │ double          │ 8               │ 8字节对齐           │ │
│ │ uint32_t        │ 4               │ 4字节对齐           │ │
│ │ float           │ 4               │ 4字节对齐           │ │
│ │ uint16_t        │ 2               │ 2字节对齐           │ │
│ └─────────────────┴─────────────────┴─────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 内存访问优化:                                               │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 1. 使用memcpy进行批量数据拷贝                           │ │
│ │ 2. 避免非对齐的内存访问                                 │ │
│ │ 3. 预分配足够的缓冲区大小                               │ │
│ │ 4. 使用模板函数处理不同数据类型                         │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **文件 I/O 优化策略**

```
I/O优化技术:
┌─────────────────────────────────────────────────────────────┐
│ 1. 顺序读取策略                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ - 利用操作系统的预读缓存                                │ │
│ │ - 减少随机访问，提高磁盘效率                            │ │
│ │ - 批量读取多个连续帧                                    │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 2. 内存映射                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ - 对于大文件使用内存映射                                │ │
│ │ - 减少数据拷贝开销                                      │ │
│ │ - 利用虚拟内存管理                                      │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 3. 压缩算法选择                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ - Qt的qCompress/qUncompress (基于zlib)                 │ │
│ │ - 平衡压缩率和解压速度                                  │ │
│ │ - 适合质谱数据的特点                                    │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 **扩展性设计**

### **版本兼容性机制**

```
版本控制策略:
┌─────────────────────────────────────────────────────────────┐
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 当前版本: V1.0                                          │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ StreamHead结构固定                                  │ │ │
│ │ │ 参数块可扩展                                        │ │ │
│ │ │ Data Section格式固定                               │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 未来版本: V2.0 (假设)                                   │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ 新增版本字段                                        │ │ │
│ │ │ 新增GPS坐标字段                                     │ │ │
│ │ │ 新增温度传感器数据                                  │ │ │
│ │ │ 向后兼容V1.0格式                                   │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **新字段扩展示例**

```
V2.0扩展示例:
┌─────────────────────────────────────────────────────────────┐
│ 新的行结构:                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ [帧索引][时间戳][TIC_X][TIC_Y][经度][纬度][XIC...][其他]│ │
│ │  8字节   8字节   8字节  8字节  8字节 8字节              │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 新的偏移量计算:                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ offsetTimestamp = 8                                     │ │
│ │ offsetTIC_X = 16                                        │ │
│ │ offsetTIC_Y = 24                                        │ │
│ │ offsetLongitude = 32                                    │ │
│ │ offsetLatitude = 40                                     │ │
│ │ offsetXIC = 48                                          │ │
│ │ offsetOtherLine = 48 + 8*numXIC                         │ │
│ │ totalLineSize = 48 + 8*(numXIC + numOtherLine)          │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

这个详细的文件结构文档为理解和处理质谱数据文件提供了完整的技术参考，包括实际数据示例、内存布局、性能优化和扩展性设计。
