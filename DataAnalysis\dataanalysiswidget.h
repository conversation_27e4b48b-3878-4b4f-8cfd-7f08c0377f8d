#ifndef DATAANALYSISWIDGET_H
#define DATAANALYSISWIDGET_H

#include <QMainWindow>
#include <QMutex>
#include <QThread>
#include <QTimer>
#include <QDateTime>
#include <QTreeWidget>
#include <QScrollArea>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QProgressBar>
#include <QFileDialog>
#include <QCoreApplication>

#include "FileData/datareader.h"
#include "FileData/filedatamanager.h"
#include "FileData/filedata.h"
#include "LxChart/lxmasschart.h"
#include "LxChart/lxticxicchart.h"
#include "LxChart/lxchartdata.h"
#include "TaskManager/taskmanager.h"

namespace Ui {
class DataAnalysisWidget;
}

/**
 * @brief DataAnalysisWidget 数据分析窗口类
 * 
 * 按照示例项目DataAnalysisHZH中sDataAnalysis类的设计，
 * 实现TIC/MASS/XIC数据的加载、显示和分析功能
 */
class DataAnalysisWidget : public QMainWindow
{
    Q_OBJECT

public:
    // 线程类型枚举，对应示例项目中的_TYPE_THREAD
    enum ThreadType {
        TYPE_TIC,           // TIC数据加载
        TYPE_PAGE_TIC,      // 分页TIC数据加载
        TYPE_MASS,          // MASS数据加载
        TYPE_MASS_RANGE     // 范围MASS数据加载
    };

    explicit DataAnalysisWidget(QWidget* pParentToolBar, QWidget *parent = nullptr);
    ~DataAnalysisWidget();

    // 初始化函数
    virtual void initialize();
    
    // 数据显示函数，对应示例项目中的showDataTIC等函数
    virtual bool showDataTIC(QString fileName = QString());
    bool showDataTIC(qint64 page);
    bool showDataMass(ThreadType threadType);

    // 获取当前线程类型
    ThreadType currentType() const { return m_threadType; }

    // 获取当前文件列表
    QStringList& currentFileTIC() { return mFileNameTIC; }

    // 获取当前帧列表
    QList<qint64>& currentFrame() { return mFrame; }

    // 数据访问函数
    QList<std::vector<qint64>>& getIndexArray() { return mIndexArray; }
    QList<std::vector<double>>& getDataTIC_X() { return mDataTIC_X; }
    QList<std::vector<double>>& getDataTIC_Y() { return mDataTIC_Y; }

protected:
    // 线程处理函数，对应示例项目中的analyzeThread
    static int analyzeThread(void *pParam, const bool &bRunning);
    
    // 数据加载函数，对应示例项目中的loadFileThreadXXX函数
    int loadFileThreadTIC();
    int loadFileThreadPageTIC();
    int loadFileThreadMass();
    int loadFileThreadMassRange();

    // 数据解析函数，对应示例项目中的dataDisassembleFirst
    uint32_t dataDisassembleFirst(QByteArray& pByteArray,
                                  cParamValue::_Segment* pSegment,
                                  QList<std::vector<double>>& pListX,
                                  QList<std::vector<double>>& pListY,
                                  QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>>& pSTRUCT_DATA,
                                  bool restart = false);

    // 头文件分解函数，对应示例项目中的splitStreamHead
    int splitStreamHead(QByteArray& segment,
                        QByteArray& pStreamHead,
                        QString& pPropertyStr,
                        QByteArray& pTuneFile);

private slots:
    // UI事件处理
    void on_UI_PB_LOAD_clicked();
    void onSelectedTIC(const double x);
    void onUpdateGraphTIC(bool deletePlots);
    void onUpdateGraphMass(int eventCount);
    void onUpdatePageTIC(qint64 pages);

private:
    Ui::DataAnalysisWidget *ui;
    
    // 图表组件
    LxMassChart *mChartMass;
    LxTicXicChart *mChartTIC;
    
    // 数据管理
    FileDataManager *fileDataManager;
    DataReader *dataReader;
    
    // 线程管理
    QThread *mAnalyzeThread;
    ThreadType m_threadType;
    bool isRestart;
    qint64 mCurrentPage;
    
    // 数据存储，对应示例项目中的数据结构
    QStringList mFileNameTIC;
    QList<qint64> mFrame;
    
    // TIC数据
    QList<std::vector<qint64>> mIndexArray;
    QList<std::vector<double>> mDataTIC_X;
    QList<std::vector<double>> mDataTIC_Y;
    QList<QList<std::vector<double>>> mDataOtherLines_Y;
    
    // MASS数据
    QList<QByteArray> mGraphBuffer;
    QList<QByteArray> mSegment;
    QList<QString> mStrProperty;
    QList<QString> mTuneFilePath;
    QList<QList<std::vector<double>>> mGraphBuffX;
    QList<QList<std::vector<double>>> mGraphBuffY;
    
    // 流数据
    QList<QByteArray> mStreamHead;
    QList<QByteArray> mStreamBody;
    QList<QList<std::vector<double>>> tmpThreadBuffX;
    QList<QList<std::vector<double>>> tmpThreadBuffY;
    QList<QVector<qint64>> mPageTIC;
    
    // 线程安全
    QMutex mGraphBuffMutexTIC;
    QMutex mGraphBuffMutexMass;
    
    // 配置和状态
    QString mChartHead;
    QString mStrSelectXIC;
    QDateTime mStartTime;
    QList<QPair<uint, uint>> mFrameRange; // 用于TYPE_MASS_RANGE

signals:
    // 对应示例项目中的信号
    void sUpdateGraphMass(int eventCount);
    void sUpdateGraphTIC(bool deletePlots);
    void sUpdatePageTIC(qint64 pages);
    void sUpdateMSG(QString message);
};

#endif // DATAANALYSISWIDGET_H
