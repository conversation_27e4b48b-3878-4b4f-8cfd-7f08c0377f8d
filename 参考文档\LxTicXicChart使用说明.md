# LxTicXicChart 使用说明

## 概述

`LxTicXicChart` 是一个新的图表类，用于同时显示TIC（总离子流）和XIC（提取离子流）曲线。该类继承自 `LxChart`，提供了TIC和XIC数据的一对一关联显示功能。

## 主要特性

1. **TIC和XIC曲线一对一关联**：通过 `tic_event_id` 字段关联TIC和XIC数据
2. **颜色同步**：关联的TIC和XIC曲线使用相同的颜色
3. **双击显示MASS数据**：保持原有的双击TIC曲线显示MASS数据的逻辑
4. **XIC曲线基础交互**：XIC曲线支持悬停变红、单击变粗，但不参与计算
5. **完整的数据管理**：提供添加、删除、查询TIC/XIC数据对的完整接口

## 类结构

### 构造函数
```cpp
explicit LxTicXicChart(QWidget *parent = nullptr);
```

### 主要方法

#### 数据管理
```cpp
// 添加TIC数据
void addTicData(TicChartData *ticData);

// 添加XIC数据
void addXicData(XicChartData *xicData);

// 添加TIC和XIC数据对
void addTicXicPair(TicChartData *ticData, XicChartData *xicData);

// 通过事件ID移除数据对
bool removeTicXicPairByEventId(int eventId);

// 清除所有数据
void clearAllTicXicData();
```

#### 数据查询
```cpp
// 获取数据列表
QList<TicChartData*> getTicDataList() const;
QList<XicChartData*> getXicDataList() const;

// 通过事件ID获取数据
TicChartData* getTicDataByEventId(int eventId) const;
XicChartData* getXicDataByEventId(int eventId) const;
```

### 信号
```cpp
// TIC和XIC数据对添加完成信号
void ticXicPairAdded(int eventId);

// TIC和XIC数据对移除完成信号
void ticXicPairRemoved(int eventId);
```

## 使用示例

### 基本使用
```cpp
// 创建图表
LxTicXicChart *chart = new LxTicXicChart(this);

// 设置交互模式
chart->enablePointSelection(true);
chart->setInteractionMode(GlobalEnums::InteractionMode::Mode_Select);

// 连接信号
connect(chart, &LxChart::sg_showMassChart, 
        fileDataManager, &FileDataManager::showMassChart);
```

### 添加数据
```cpp
// 方式1：分别添加TIC和XIC数据
TicChartData *ticData = new TicChartData(paramPath, ionMode, scanMode, 
                                         dataProcess, dataName, sampleName, eventNum);
XicChartData *xicData = new XicChartData(paramPath, ionMode, scanMode, 
                                         dataProcess, dataName, sampleName, eventNum);
xicData->setTic_event_id(eventNum); // 设置关联的TIC事件ID

chart->addTicData(ticData);
chart->addXicData(xicData);

// 方式2：同时添加TIC和XIC数据对
chart->addTicXicPair(ticData, xicData);
```

### 数据查询和管理
```cpp
// 获取特定事件的数据
int eventId = 1;
TicChartData *ticData = chart->getTicDataByEventId(eventId);
XicChartData *xicData = chart->getXicDataByEventId(eventId);

// 获取所有数据
QList<TicChartData*> allTicData = chart->getTicDataList();
QList<XicChartData*> allXicData = chart->getXicDataList();

// 移除特定事件的数据
chart->removeTicXicPairByEventId(eventId);

// 清除所有数据
chart->clearAllTicXicData();
```

## 关键设计特点

### 1. 数据关联机制
- TIC数据通过 `getEventNum()` 获取事件ID
- XIC数据通过 `getTic_event_id()` 获取关联的TIC事件ID
- 两者必须匹配才能形成有效的数据对

### 2. 颜色管理
- 在添加数据时自动确保TIC和XIC的颜色一致
- 使用 `ensureColorConsistency()` 方法同步颜色
- 支持运行时颜色同步（如果基类提供相应信号）

### 3. 交互处理
- 重写 `eventFilter()` 方法处理双击事件
- 只有点击在TIC曲线上的双击才会触发显示MASS数据
- XIC曲线仅支持基础的视觉交互

### 4. 内存管理
- 继承基类的数据管理机制
- 提供完整的清理方法
- 在析构时自动清理所有资源

## 与原有类的关系

### 替代方案
`LxTicXicChart` 可以作为 `LxTicChart` 和 `LxXicChart` 的组合替代方案：

```cpp
// 原有方式
LxTicChart *ticChart = new LxTicChart(GlobalEnums::TrackType::TIC, this);
LxXicChart *xicChart = new LxXicChart(GlobalEnums::TrackType::XIC, this);

// 新的组合方式
LxTicXicChart *ticXicChart = new LxTicXicChart(this);
```

### 迁移建议
1. 保留原有的 `LxTicChart` 和 `LxXicChart` 用于单独显示
2. 在需要同时观察TIC和XIC关系时使用 `LxTicXicChart`
3. 逐步测试和验证新类的功能完整性
4. 确认所有信号连接和数据流正常工作后，可考虑完全替换

## 注意事项

1. **事件ID匹配**：确保TIC和XIC数据的事件ID正确匹配
2. **颜色同步**：如需运行时颜色同步，可能需要扩展基类的信号机制
3. **性能考虑**：同时显示多条曲线时注意性能影响
4. **数据一致性**：确保TIC和XIC数据来源于同一个实验和事件

## 后续扩展

1. 支持更多类型的曲线关联（如DAD、TWC等）
2. 增强颜色管理和主题支持
3. 添加更多的数据分析和比较功能
4. 优化大数据量下的显示性能
