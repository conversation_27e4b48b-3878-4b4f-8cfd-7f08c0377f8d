#include "wh_signal_smothing.h"
#include <vector>
//#include <numeric>
//#include <iostream>
//#include <Eigen/Dense>

// 简单移动平均（Simple Moving Average）
std::vector<double> movingAverage(const std::vector<double>& data, size_t  windowSize)
{

    if (windowSize <= 0 || windowSize > data.size())
    {
        throw std::invalid_argument("Invalid window size");
    }

    std::vector<double> smoothed;
    // smoothed.reserve(data.size() - windowSize + 1);
    for (size_t k = 0; k<windowSize; k++)
    {
        smoothed.push_back(data[k]); //前windowSize个数据直接copy
    }

    for (size_t i = windowSize; i < data.size() - windowSize; i++)
    {
        double sum = 0.0;
        for (size_t  j = 0; j < windowSize; j++)
        {
            sum += data[i + j];
        }
        smoothed.push_back(sum / windowSize);
    }

    for (size_t j = data.size() - windowSize; j<data.size(); j++)
    {
        smoothed.push_back(data[j]); //后windowSize个数据直接copy
    }

    return smoothed;
}

// 生成高斯核
std::vector<double> gaussianKernel(int size, double sigma)
{
    std::vector<double> kernel(size);
    double sum = 0.0;
    int radius = size / 2;

    for (int i = -radius; i <= radius; ++i)
    {
        double val = std::exp(-(i * i) / (2 * sigma * sigma));
        kernel[i + radius] = val;
        sum += val;
    }

    // 归一化
    for (double& val : kernel)
    {
        val /= sum;
    }

    return kernel;
}

// 高斯滤波
std::vector<double> gaussianSmooth(const std::vector<double>& data, int kernelSize, double sigma)
{
    if (kernelSize % 2 == 0)
    {
        throw std::invalid_argument("Kernel size must be odd");
    }

    auto kernel = gaussianKernel(kernelSize, sigma);
    int radius = kernelSize / 2;
    std::vector<double> smoothed(data.size(), 0.0);

    for (size_t i = radius; i < data.size() - radius; ++i)
    {
        double sum = 0.0;
        for (int j = -radius; j <= radius; ++j)
        {
            sum += data[i + j] * kernel[j + radius];
        }
        smoothed[i] = sum;
    }

    return smoothed;
}

// 计算多项式拟合系数
Eigen::VectorXd polyfit(const Eigen::VectorXd& x, const Eigen::VectorXd& y, int degree)
{
    Eigen::MatrixXd A(x.size(), degree + 1);
    for (int i = 0; i < x.size(); ++i)
    {
        for (int j = 0; j <= degree; ++j)
        {
            A(i, j) = std::pow(x(i), j);
        }
    }
    return A.householderQr().solve(y);
}

// Savitzky-Golay 滤波
std::vector<double> savitzkyGolay(const std::vector<double>& data, int windowSize, int polyOrder)
{
    if (windowSize % 2 == 0)
    {
        throw std::invalid_argument("Window size must be odd");
    }

    int radius = windowSize / 2;
    std::vector<double> smoothed(data.size(), 0.0);

    Eigen::VectorXd x(windowSize);
    for (int i = -radius; i <= radius; ++i)
    {
        x(i + radius) = i;
    }

    for (size_t i = radius; i < data.size() - radius; ++i)
    {
        Eigen::VectorXd y(windowSize);
        for (int j = -radius; j <= radius; ++j)
        {
            y(j + radius) = data[i + j];
        }

        Eigen::VectorXd coeffs = polyfit(x, y, polyOrder);
        smoothed[i] = coeffs(0);  // 取拟合中心点的值
    }
    return smoothed;
}

int example_of_invoke_smoothing()
{
    std::vector<double> data = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 };
    int windowSize = 5;
    std::vector<double> smoothed = movingAverage(data, windowSize);

    int kernelSize = 5;
    double sigma = 1.0;
    std::vector<double> smoothed_gaussian = gaussianSmooth(data, kernelSize, sigma);

    int windowSize_poly = 5;
    int polyOrder = 2;
    std::vector<double> smoothed_poly = savitzkyGolay(data, windowSize_poly, polyOrder);

    return 0;
}
