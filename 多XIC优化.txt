XIC BUG:
1.第一个TIC的XIC被限制只有3个
2.第二个TIC的XIC只有1个
3.删除第一个TIC的某个XIC之后先是第一个TIC以及它的其余XIC的LxChartLegend都不显示了，但是鼠标悬停有明显的轮廓，随后就崩溃了
4.除了每个TIC的第一个XIC可以被鼠标交互之外，其他XIC都无法交互
5.同一个TIC下的XIC颜色一样，没有独立随机的颜色



LxChartLegend: 准备删除，路径: "C:/Users/<USER>/Desktop/ER35_HV_Test2.Param" ，UniqueID: "e509a03d-1122-4a9d-886a-f5892f7d2473"
发送方: "C:/Users/<USER>/Desktop/ER35_HV_Test2.Param" "e509a03d-1122-4a9d-886a-f5892f7d2473"
尝试移除ID为 "e509a03d-1122-4a9d-886a-f5892f7d2473" 的控件 6
当前ID: "d155ec25-bdc9-4802-8a44-914d3c2ef1e2"
当前ID: "9e09f89c-296a-4833-97d3-b95cddbe0ce2"
当前ID: "c060b721-7dbb-47e3-98a6-1284df1d9f22"
当前ID: "e509a03d-1122-4a9d-886a-f5892f7d2473"
成功移除ID为 "e509a03d-1122-4a9d-886a-f5892f7d2473" 的控件
LxTicXicChart::UpdateGlobalRange: 开始更新全局范围，考虑TIC/XIC可见性
LxTicXicChart::UpdateGlobalRange: 包含数据，UniqueID: "d155ec25-bdc9-4802-8a44-914d3c2ef1e2" ，类型: TIC
LxTicXicChart::UpdateGlobalRange: XIC数据可见性: true ，UniqueID: "9e09f89c-296a-4833-97d3-b95cddbe0ce2"
LxTicXicChart::UpdateGlobalRange: 包含数据，UniqueID: "9e09f89c-296a-4833-97d3-b95cddbe0ce2" ，类型: XIC
LxTicXicChart::UpdateGlobalRange: XIC数据可见性: true ，UniqueID: "c060b721-7dbb-47e3-98a6-1284df1d9f22"
LxTicXicChart::UpdateGlobalRange: 包含数据，UniqueID: "c060b721-7dbb-47e3-98a6-1284df1d9f22" ，类型: XIC
LxTicXicChart::UpdateGlobalRange: 包含数据，UniqueID: "8a944fee-bc43-4313-a630-8f37de727d9a" ，类型: TIC
LxTicXicChart::UpdateGlobalRange: XIC数据可见性: true ，UniqueID: "f67fb488-4b4d-487b-b39a-d3f80936dcc0"
LxTicXicChart::UpdateGlobalRange: 包含数据，UniqueID: "f67fb488-4b4d-487b-b39a-d3f80936dcc0" ，类型: XIC
LxTicXicChart::UpdateGlobalRange: 更新全局范围:
  X轴:  0  到  33.4167
  Y轴:  27200.3  到  1.294e+07
LxChart::SetAxisScale: 准备设置X轴范围: 0 到 33.4167
LxChart::SetAxisScale: X轴范围设置完成
LxChart::SetAxisScale: 准备设置Y轴范围: 27200.3 到 1.294e+07
LxChart::SetAxisScale: Y轴范围设置完成
LxChart::SetAxisScale: 准备设置Y轴格式和标题
LxChart::SetAxisScale: Y轴格式和标题设置完成
移除曲线，UniqueID: "e509a03d-1122-4a9d-886a-f5892f7d2473" ，名称: "e509a03d-1122-4a9d-886a-f5892f7d2473"
XIC删除：事件ID 0 ，不影响TIC和MASS
LxTicXicChart::RemoveLxChartDataByUniqueID: 移除XIC数据，UniqueID: "e509a03d-1122-4a9d-886a-f5892f7d2473"
LxChartLegend: 删除信号已发射，路径: "C:/Users/<USER>/Desktop/ER35_HV_Test2.Param"
LxChart::RemoveLxChartDataByUniqueID: 延迟发射sg_removeData信号
FileDataManager::removeData: 方法开始，路径: "C:/Users/<USER>/Desktop/ER35_HV_Test2.Param"
FileDataManager::removeData: 找到路径，开始移除: "C:/Users/<USER>/Desktop/ER35_HV_Test2.Param"
FileDataManager::removeData: 准备删除FileData对象，路径: "C:/Users/<USER>/Desktop/ER35_HV_Test2.Param" ，指针: FileData(0x321419e0)
FileDataManager::removeData: FileData对象有效，路径验证: "C:/Users/<USER>/Desktop/ER35_HV_Test2.Param"
FileDataManager::removeData: 开始删除FileData对象，当前线程: QThread(0x2bb39da8)
FileData::~FileData: 开始析构，路径: "C:/Users/<USER>/Desktop/ER35_HV_Test2.Param" ，当前线程: QThread(0x2bb39da8)
FileData::~FileData: 清理TicMap，数量: 1
FileData::~FileData: 删除TicChartData，事件ID: 0
TicChartData::~TicChartData: 开始析构，事件ID: 0 ，当前线程: QThread(0x2bb39da8)
TicChartData::deleteMassData: 删除MASS数据，事件ID: 0
LxChartData::~LxChartData: 开始析构，UniqueID: "9d18ca19-f16f-42e8-9be0-0991e9f56b71"
LxChartData::~LxChartData: 删除关联的LxChartLegend
LxChartData::~LxChartData: 基类清理QLineSeries
LxChartData::deleteSeries: 删除QLineSeries，名称: "9d18ca19-f16f-42e8-9be0-0991e9f56b71"
LxChartData::deleteSeries: 从图表中移除QLineSeries
LxChartData::deleteSeries: QLineSeries删除完成
LxChartData::~LxChartData: 析构完成，UniqueID: "9d18ca19-f16f-42e8-9be0-0991e9f56b71"
TicChartData::deleteMassData: MASS数据删除成功
TicChartData::deleteAllXicData: 删除所有XIC数据，事件ID: 0 ，当前XIC数量: 4
TicChartData::deleteAllXicData: 删除XIC，ID: "{5869e54a-e4d9-42b7-b1ec-55237f5b6a52}"
XicChartData::~XicChartData: 开始析构
XicChartData::~XicChartData: 临时跳过QLineSeries清理，让基类处理
XicChartData::~XicChartData: 析构完成
LxChartData::~LxChartData: 开始析构，UniqueID: "e509a03d-1122-4a9d-886a-f5892f7d2473"
LxChartData::~LxChartData: QLineSeries已被清理，跳过
LxChartData::~LxChartData: 析构完成，UniqueID: "e509a03d-1122-4a9d-886a-f5892f7d2473"
TicChartData::deleteAllXicData: 删除XIC，ID: "{95d815a7-fcfb-4612-b7d1-6ae4f5010892}"
XicChartData::~XicChartData: 开始析构
XicChartData::~XicChartData: 临时跳过QLineSeries清理，让基类处理
XicChartData::~XicChartData: 析构完成
LxChartData::~LxChartData: 开始析构，UniqueID: "730065dd-cdbd-4ee6-8635-36ada481bda0"
LxChartData::~LxChartData: QLineSeries已被清理，跳过
LxChartData::~LxChartData: 析构完成，UniqueID: "730065dd-cdbd-4ee6-8635-36ada481bda0"
TicChartData::deleteAllXicData: 删除XIC，ID: "{ac2093fd-2a8a-421e-91cd-2940c44ece68}"
XicChartData::~XicChartData: 开始析构
XicChartData::~XicChartData: 临时跳过QLineSeries清理，让基类处理
XicChartData::~XicChartData: 析构完成
LxChartData::~LxChartData: 开始析构，UniqueID: "9e09f89c-296a-4833-97d3-b95cddbe0ce2"
LxChartData::~LxChartData: 删除关联的LxChartLegend
LxChartData::~LxChartData: QLineSeries已被清理，跳过
LxChartData::~LxChartData: 析构完成，UniqueID: "9e09f89c-296a-4833-97d3-b95cddbe0ce2"
TicChartData::deleteAllXicData: 删除XIC，ID: "{b2eec0ce-3397-4c6c-8971-f4fb31311ee6}"
XicChartData::~XicChartData: 开始析构
XicChartData::~XicChartData: 临时跳过QLineSeries清理，让基类处理
XicChartData::~XicChartData: 析构完成
LxChartData::~LxChartData: 开始析构，UniqueID: "c060b721-7dbb-47e3-98a6-1284df1d9f22"
LxChartData::~LxChartData: 删除关联的LxChartLegend
LxChartData::~LxChartData: QLineSeries已被清理，跳过
LxChartData::~LxChartData: 析构完成，UniqueID: "c060b721-7dbb-47e3-98a6-1284df1d9f22"
TicChartData::deleteAllXicData: 所有XIC数据删除完成
TicChartData::~TicChartData: 析构完成，事件ID: 0
LxChartData::~LxChartData: 开始析构，UniqueID: "d155ec25-bdc9-4802-8a44-914d3c2ef1e2"
LxChartData::~LxChartData: 删除关联的LxChartLegend
LxChartData::~LxChartData: 基类清理QLineSeries
LxChartData::deleteSeries: 删除QLineSeries，名称: "d155ec25-bdc9-4802-8a44-914d3c2ef1e2"
LxChartData::deleteSeries: 从图表中移除QLineSeries
LxChartData::deleteSeries: QLineSeries删除完成
LxChartData::~LxChartData: 析构完成，UniqueID: "d155ec25-bdc9-4802-8a44-914d3c2ef1e2"
FileData::~FileData: 析构完成，所有数据已清理
FileDataManager::removeData: FileData对象已在主线程中删除
FileDataManager::removeData: FileData指针已置空
FileDataManager::removeData: 从映射中移除路径: "C:/Users/<USER>/Desktop/ER35_HV_Test2.Param"
FileDataManager::removeData: 映射移除完成，当前映射大小: 1
FileDataManager::removeData: 方法完成，返回true
QGraphicsScene::addItem: item has already been added to this scene
双击大小改变
双击大小改变
双击大小改变
ASSERT failure in QVector<T>::at: "index out of range", file D:/Qt5.12.5/5.12.5/mingw73_32/include/QtCore/qvector.h, line 429
18:26:15: 进程崩溃了。