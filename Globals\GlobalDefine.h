#ifndef GLOBALDEFINE_H
#define GLOBALDEFINE_H
#include <qglobal.h>
#include <QString>
#include <QVector>
#include <QPointF>
#include <QDebug>
#include <QColor>
#include <QGraphicsLineItem>
#include <QCoreApplication>
#include <QStandardPaths>
#include "GlobalEnums.h"
#include "definecommands.h"

namespace GlobalDefine
{

    //*全局分析配置 */////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////
    // 模拟示例项目的_CONGIG_ANALYSIS结构体
    struct AnalysisConfig
    {
        double Period;          // 分页时间限制（秒）
        int maxiHeighMassChart; // 最大质谱图高度

        // 默认构造函数
        AnalysisConfig() : Period(86400000.0), maxiHeighMassChart(16777215) {}
    };

    // 全局分析配置管理器
    class GlobalAnalysisConfig
    {
    public:
        // 获取单例实例
        static GlobalAnalysisConfig &getInstance();

        // 获取配置对象（模拟示例项目的getConfig()函数）
        static AnalysisConfig *getConfig();

        // 加载配置文件（模拟示例项目的loadConfigFile()函数）
        static void loadConfigFile();

    private:
        GlobalAnalysisConfig() { loadConfigFile(); }
        ~GlobalAnalysisConfig() = default;
        GlobalAnalysisConfig(const GlobalAnalysisConfig &) = delete;
        GlobalAnalysisConfig &operator=(const GlobalAnalysisConfig &) = delete;

        static AnalysisConfig m_config;
    };

    //*路径相关 */////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////
    class GlobalPaths
    {
    public:
        // 获取单例实例
        static GlobalPaths &getInstance();

        // 静态函数获取路径信息
        static QString getExePath();

        static QString getConfigDir();

        // 移除了PeakFindingConfig.xml相关方法

    private:
        // 私有构造函数
        GlobalPaths() {}
        // 禁止拷贝构造和赋值
        GlobalPaths(const GlobalPaths &) = delete;
        GlobalPaths &operator=(const GlobalPaths &) = delete;
    };

    //*路径相关 */////////////⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️///////////////
    const double THRESHOLDDEST = 10.0; // 判断点是否曲线上的像素阈值（用于高亮当前曲线）
    const uint LINE_WIDTH = 1;         // 默认曲线宽度
    const uint WIDTH_INCREMENT = 2;    // 高亮曲线宽度增量
    const uint CUSTOM_RANGE_SIZE = 1;  // 自定义区域数量
    const uint CUSTOM_LABEL_SIZE = 3;  // 自定义标注数量
    struct PointCloud
    {
        std::vector<QPointF> points;

        double maxX, minX, maxY, minY;

        inline size_t kdtree_get_point_count() const { return points.size(); }
        inline double kdtree_get_pt(const size_t idx, int dim) const { return (dim == 0) ? points[idx].x() : points[idx].y(); }

        // kd-tree 接口：返回第 idx 个点的 dim 维坐标
        // 这里直接做归一化
        // inline double kdtree_get_pt(const size_t idx, int dim) const
        // {
        //     const auto &p = points[idx];
        //     if (dim == 0) {
        //         // 确保 x 和 y 的范围合理
        //         if (maxX == minX)
        //             return 0.5; // 防止除以零

        //         // x 归一化到 [0,1]
        //         double normalizedX = (p.x() - minX) / (maxX - minX);
        //         // 限制在0-1范围内
        //         return qBound(0.0, normalizedX, 1.0);
        //     } else {
        //         if (maxY == minY)
        //             return 0.5; // 防止除以零

        //         // y 归一化到 [0,1]
        //         double normalizedY = (p.y() - minY) / (maxY - minY);
        //         // 限制在0-1范围内
        //         qDebug() << "返回归一化的点:" << qBound(0.0, normalizedY, 1.0);
        //         return qBound(0.0, normalizedY, 1.0);
        //     }
        // }

        template <class BBOX>
        bool kdtree_get_bbox(BBOX & /*bb*/) const
        {
            return false;
        }
    };

    // MRM数据结构体
    struct StructMRM
    {
        uint Experiment;
        QString ID;
        double Q1;
        double Q3;
        double RT;
        double massPre;
        double massPreOrig;
        double timeMs;

        double mass;
        double massOrig;
    };

    // LxChart自定义区域
    struct CustomRange
    {
        QPair<qreal, qreal> range;
        QColor color;
        int index; // index即是添加此结构体进入数组时的数组大小（每次添加都是最后一个位置元素）

        QGraphicsLineItem *m_StartLine;
        QGraphicsLineItem *m_EndLine;
        QGraphicsRectItem *m_Rect;
    };

} // namespace GlobalDefine
#endif // GLOBALDEFINE_H
