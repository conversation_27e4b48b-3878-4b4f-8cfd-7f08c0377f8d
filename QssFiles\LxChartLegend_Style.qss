/* ========================================
   LxChartLegend 专用样式文件
   ======================================== */

/* ========================================
   主容器样式
   ======================================== */

LxChartLegend:hover {
  background-color: rgb(240, 240, 240);
  border-color: #d0d0d0;
}

/* ========================================
   颜色标签样式 (label_legend)
   ======================================== */
QLabel#label_legend {
  border: 1px solid #888888;
  border-radius: 2px;
  min-width: 10px;
  max-width: 10px;
  min-height: 10px;
  max-height: 10px;
}

/* ========================================
   类型按钮样式 (btn_type)
   ======================================== */
QPushButton#btn_type {
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 3px;

  min-width: 30px;
  max-width: 30px;
  min-height: 30px;
  max-height: 30px;
}

QPushButton#btn_type:hover {
  background-color: rgba(0, 120, 215, 0.1);
  border-color: rgba(0, 120, 215, 0.3);
}

QPushButton#btn_type:pressed {
  background-color: rgba(0, 120, 215, 0.2);
  border-color: rgba(0, 120, 215, 0.5);
}

/* ========================================
   名称按钮样式 (btn_name)
   ======================================== */
QPushButton#btn_name {
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 3px;
  text-align: left;
  font-size: 20px;
  font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
  color: #000000;
  min-height: 20px;
}

QPushButton#btn_name:hover {
  background-color: rgba(0, 120, 215, 0.1);
  border-color: rgba(0, 120, 215, 0.3);
  color: #0078d7;
}

QPushButton#btn_name:pressed {
  background-color: rgba(0, 120, 215, 0.2);
  border-color: rgba(0, 120, 215, 0.5);
  color: #106ebe;
}

/* ========================================
   工具提示样式
   ======================================== */
QToolTip {
  background-color: #2b2b2b;
  color: #ffffff;
  border: 1px solid #555555;
  border-radius: 4px;
  font-size: 20px;
  font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
  font-weight: normal;
  padding: 6px 10px;
}

/* ========================================
   右键菜单样式
   ======================================== */
QMenu {
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 4px;
  text-align: left;
  font-size: 18px;
  font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
}

QMenu::item {
  background-color: transparent;
  padding: 6px 20px 6px 30px;
  color: #333333;
}

QMenu::item:selected {
  background-color: #e3f2fd;
  color: #0078d7;
}

QMenu::item:pressed {
  background-color: #bbdefb;
  color: #0078d7;
}

QMenu::separator {
  height: 1px;
  background-color: #e0e0e0;
  margin: 4px 10px;
}

QMenu::icon {
  padding-left: 10px;
}

/* ========================================
   选中/激活状态样式
   ======================================== */
LxChartLegend[selected="true"] {
  background-color: rgba(0, 120, 215, 0.1);
  border-color: #0078d7;
}

LxChartLegend[selected="true"] QPushButton#btn_name {
  color: #0078d7;
  font-weight: bold;
}

/* 选中状态的btn_name按钮样式（通过代码动态设置） */
QPushButton#btn_name[selected="true"] {
  border: 2px solid #0078d7;
  background-color: rgba(0, 120, 215, 0.1);
  color: #0078d7;
  font-weight: bold;
  border-radius: 3px;
}

/* ========================================
   禁用状态样式
   ======================================== */
LxChartLegend:disabled {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
  opacity: 0.6;
}

LxChartLegend:disabled QPushButton#btn_type {
  opacity: 0.5;
}
