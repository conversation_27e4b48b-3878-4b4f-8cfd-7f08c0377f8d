HEADERS += \
    $$PWD/ComboBoxSelector.h \
    $$PWD/CustomSplitter.h \
    $$PWD/Dialogs/customlabelinputdialog.h \
    $$PWD/Dialogs/customlabelwidget.h \
    $$PWD/Dialogs/customlegendwidget.h \
    $$PWD/Dialogs/linewidthdialog.h \
    $$PWD/Dialogs/optionsdialog.h

SOURCES += \
    $$PWD/ComboBoxSelector.cpp \
    $$PWD/CustomSplitter.cpp \
    $$PWD/Dialogs/customlabelinputdialog.cpp \
    $$PWD/Dialogs/customlabelwidget.cpp \
    $$PWD/Dialogs/customlegendwidget.cpp \
    $$PWD/Dialogs/linewidthdialog.cpp \
    $$PWD/Dialogs/optionsdialog.cpp

FORMS += \
    $$PWD/Dialogs/customlabelinputdialog.ui \
    $$PWD/Dialogs/customlabelwidget.ui \
    $$PWD/Dialogs/customlegendwidget.ui \
    $$PWD/Dialogs/linewidthdialog.ui \
    $$PWD/Dialogs/optionsdialog.ui