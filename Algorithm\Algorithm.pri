# Eigen库的路径已在主项目文件中定义，这里不需要重复定义
# 如果遇到问题，可以取消注释下面的行
# INCLUDEPATH += D:\\StudyAndWork\\Eigen
# INCLUDEPATH += D:\\StudyAndWork\\Eigen\\Eigen
# 添加Eigen库路径
INCLUDEPATH += D:\\StudyAndWork


HEADERS += \
    $$PWD/PeakFind/peakfind.h \
    $$PWD/PeakFind/wh_baselineCorrect.h \
    $$PWD/PeakFind/wh_peakSearch.h \
    $$PWD/PeakFind/wh_signal_smothing.h

SOURCES += \
    $$PWD/PeakFind/peakfind.cpp \
    $$PWD/PeakFind/wh_baselineCorrect.cpp \
    $$PWD/PeakFind/wh_peakSearch.cpp \
    $$PWD/PeakFind/wh_signal_smothing.cpp



