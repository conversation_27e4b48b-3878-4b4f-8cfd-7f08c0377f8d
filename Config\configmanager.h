#pragma once

#include <QObject>
#include <QDomDocument>
#include <QFile>
#include <QDebug>
#include "Globals/GlobalDefine.h"

class ConfigManager : public QObject
{
    Q_OBJECT
public:
    // 获取单例实例
    static ConfigManager &getInstance();

    // 加载配置文件
    virtual bool loadConfig();

    // 保存配置文件
    static bool saveConfig();

    // 增加属性
    static QString setProperty(const QString &className, const QString &propertyName, const QString &value);

    // 删除属性
    static bool removeProperty(const QString &className, const QString &propertyName);

    // 查询属性值
    static QString getPropertyValue(const QString &className, const QString &propertyName);

    // 设置文件路径
    static void setConfigFilePath(QString path = "");

    // 是否第一次初始化
    static bool isFirstInit;

protected: // 将构造函数设为保护的，以允许子类访问
    ConfigManager();

    // 禁止拷贝构造和赋值
    ConfigManager(const ConfigManager &) = delete;
    ConfigManager &operator=(const ConfigManager &) = delete;

    static QString m_filePath; // 配置文件路径
    static QDomDocument m_doc; // XML文档对象

signals:
};
