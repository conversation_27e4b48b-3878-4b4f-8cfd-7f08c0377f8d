#include "customlegendwidget.h"
#include "ui_customlegendwidget.h"

CustomLegendWidget::CustomLegendWidget(QWidget *parent) : QWidget(parent), ui(new Ui::CustomLegendWidget)
{
    ui->setupUi(this);

    // 设置QScrollArea的滚动条策略
    ui->scrollArea_legend->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded); // 需要时显示水平滚动条
    ui->scrollArea_legend->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);   // 需要时显示垂直滚动条
    ui->scrollArea_legend->setWidgetResizable(true);                            // 允许内容控件调整大小

    // 创建内容控件和布局
    m_contentWidget = new QWidget();
    m_layout = new QVBoxLayout(m_contentWidget);
    m_layout->setContentsMargins(0, 0, 0, 0);
    m_layout->setSpacing(2); // 设置项目间距
    m_layout->addStretch();  // 添加弹性空间，让项目靠上对齐

    // 设置内容控件到滚动区域
    ui->scrollArea_legend->setWidget(m_contentWidget);

    connectAll();
}
void CustomLegendWidget::addLegendWidget(LxChartLegend *legend)
{
    // 移除弹性空间（如果存在）
    if (m_layout->count() > 0)
    {
        QLayoutItem *lastItem = m_layout->itemAt(m_layout->count() - 1);
        if (lastItem->spacerItem())
        {
            m_layout->removeItem(lastItem);
            delete lastItem;
        }
    }

    // 直接添加legend到布局中
    m_layout->addWidget(legend);

    // 重新添加弹性空间，保持项目靠上对齐
    m_layout->addStretch();

    m_uint_legendCount++;

    // 更新内容控件大小
    m_contentWidget->adjustSize();
}

void CustomLegendWidget::connectAll()
{
}

void CustomLegendWidget::resetExpandStatus()
{
    m_bool_expand = false;
}
bool CustomLegendWidget::removeLegendWidget(QString id)
{
    qDebug() << "尝试移除ID为" << id << "的控件" << m_uint_legendCount;

    // 遍历布局中的所有控件
    for (int i = 0; i < m_layout->count(); ++i)
    {
        QLayoutItem *item = m_layout->itemAt(i);
        if (item && item->widget())
        {
            LxChartLegend *legend = qobject_cast<LxChartLegend *>(item->widget());

            // 检查legend是否有效，并且id是否匹配
            if (legend)
            {
                qDebug() << "当前ID:" << legend->getUniqueID();
                if (legend->getUniqueID() == id)
                {
                    // 从布局中移除控件
                    m_layout->removeWidget(legend);
                    legend->deleteLater(); // 安全删除
                    qDebug() << "成功移除ID为" << id << "的控件";

                    m_uint_legendCount--;

                    // 更新内容控件大小
                    m_contentWidget->adjustSize();

                    return true; // 找到并移除后退出
                }
            }
        }
    }

    qDebug() << "未找到ID为" << id << "的控件";
    return false;
}

CustomLegendWidget::~CustomLegendWidget()
{
    delete ui;
}
