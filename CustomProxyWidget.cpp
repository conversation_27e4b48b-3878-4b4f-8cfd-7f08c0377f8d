#include "CustomProxyWidget.h"

CustomProxyWidget::CustomProxyWidget(QGraphicsItem *parent)
    : QGraphicsProxyWidget(parent)
{
    // 设置项标志
    setFlag(QGraphicsItem::ItemIsMovable, true);
    setFlag(QGraphicsItem::ItemIsSelectable, true);
    setFlag(QGraphicsItem::ItemSendsScenePositionChanges, true);
    
    // 设置Z值较高，确保能优先接收事件
    setZValue(100);
}

bool CustomProxyWidget::sceneEvent(QEvent *event)
{
    // 根据事件类型分发处理
    switch (event->type()) {
    case QEvent::GraphicsSceneMousePress:
        return sceneMousePressEvent(static_cast<QGraphicsSceneMouseEvent*>(event));
    case QEvent::GraphicsSceneMouseRelease:
        return sceneMouseReleaseEvent(static_cast<QGraphicsSceneMouseEvent*>(event));
    case QEvent::GraphicsSceneMouseMove:
        return sceneMouseMoveEvent(static_cast<QGraphicsSceneMouseEvent*>(event));
    case QEvent::GraphicsSceneMouseDoubleClick:
        return sceneMouseDoubleClickEvent(static_cast<QGraphicsSceneMouseEvent*>(event));
    default:
        return QGraphicsProxyWidget::sceneEvent(event);
    }
}

bool CustomProxyWidget::sceneMousePressEvent(QGraphicsSceneMouseEvent *event)
{
    // 处理鼠标按下事件
    qDebug() << "CustomProxyWidget: 鼠标按下事件" << event->button();
    
    // 将场景事件转换为控件事件并发送
    bool handled = sendMouseEventToWidget(event, QEvent::MouseButtonPress);
    
    // 如果控件已处理事件，则返回true，否则交给基类处理
    if (handled) {
        return true;
    }
    
    return QGraphicsProxyWidget::sceneEvent(event);
}

bool CustomProxyWidget::sceneMouseReleaseEvent(QGraphicsSceneMouseEvent *event)
{
    // 处理鼠标释放事件
    qDebug() << "CustomProxyWidget: 鼠标释放事件" << event->button();
    
    // 将场景事件转换为控件事件并发送
    bool handled = sendMouseEventToWidget(event, QEvent::MouseButtonRelease);
    
    if (handled) {
        return true;
    }
    
    return QGraphicsProxyWidget::sceneEvent(event);
}

bool CustomProxyWidget::sceneMouseMoveEvent(QGraphicsSceneMouseEvent *event)
{
    // 处理鼠标移动事件
    bool handled = sendMouseEventToWidget(event, QEvent::MouseMove);
    
    if (handled) {
        return true;
    }
    
    return QGraphicsProxyWidget::sceneEvent(event);
}

bool CustomProxyWidget::sceneMouseDoubleClickEvent(QGraphicsSceneMouseEvent *event)
{
    // 处理鼠标双击事件
    qDebug() << "CustomProxyWidget: 鼠标双击事件" << event->button();
    
    // 将场景事件转换为控件事件并发送
    bool handled = sendMouseEventToWidget(event, QEvent::MouseButtonDblClick);
    
    if (handled) {
        return true;
    }
    
    return QGraphicsProxyWidget::sceneEvent(event);
}

bool CustomProxyWidget::sendMouseEventToWidget(QGraphicsSceneMouseEvent *event, QEvent::Type type)
{
    QWidget *w = widget();
    if (!w) {
        return false;
    }
    
    // 将场景坐标转换为控件坐标
    QPointF pos = event->pos();
    QPointF widgetPos = mapToScene(pos);
    QPoint globalPos = event->screenPos().toPoint();
    QPoint localPos = w->mapFromGlobal(globalPos);
    
    // 创建新的鼠标事件
    QMouseEvent mouseEvent(
        type,             // 事件类型
        localPos,         // 控件内部坐标
        globalPos,        // 全局坐标
        event->button(),  // 按下的按钮
        event->buttons(), // 所有按钮状态
        event->modifiers() // 键盘修饰符
    );
    
    // 发送事件给控件
    QApplication::sendEvent(w, &mouseEvent);
    
    // 调试输出
    if (type == QEvent::MouseButtonPress || type == QEvent::MouseButtonDblClick) {
        qDebug() << "发送事件到控件:" << type << "按钮:" << event->button() 
                 << "局部坐标:" << localPos << "全局坐标:" << globalPos;
    }
    
    // 检查事件是否被接受
    return mouseEvent.isAccepted();
}