#include "avgmassmanager.h"
#include "filedata.h"
#include <QDebug>
#include <QMutexLocker>

// 静态成员初始化
QAtomicInt AvgMassManager::avgMassStatus(static_cast<int>(GlobalEnums::AvgMassStatus::Stop));
bool AvgMassManager::isRefExist = false;
QMap<QString, QMap<int, std::tuple<QVector<double>, QVector<double>, bool, int>>> AvgMassManager::avgMassMap;
QMutex AvgMassManager::avgMassMapMutex;
AvgMassManager *AvgMassManager::s_instance = nullptr;
QMutex AvgMassManager::s_instanceMutex;

AvgMassManager::AvgMassManager(QObject *parent) : QObject(parent)
{
    qDebug() << "AvgMassManager: 初始化平均质谱管理器";
}

AvgMassManager::~AvgMassManager()
{
    qDebug() << "AvgMassManager: 析构平均质谱管理器";
}

AvgMassManager *AvgMassManager::instance()
{
    QMutexLocker locker(&s_instanceMutex);
    if (!s_instance)
    {
        s_instance = new AvgMassManager();
    }
    return s_instance;
}

GlobalEnums::AvgMassStatus AvgMassManager::getAvgMassStatus()
{
    return static_cast<GlobalEnums::AvgMassStatus>(avgMassStatus.load());
}

void AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus newAvgMassStatus)
{
    int oldStatus = avgMassStatus.fetchAndStoreOrdered(static_cast<int>(newAvgMassStatus));

    if (oldStatus != static_cast<int>(newAvgMassStatus))
    {
        qDebug() << "AvgMassManager: 平均质谱状态变化:"
                 << static_cast<int>(static_cast<GlobalEnums::AvgMassStatus>(oldStatus))
                 << "->" << static_cast<int>(newAvgMassStatus);

        // 发出状态变化信号
        if (s_instance)
        {
            emit s_instance->avgMassStatusChanged(newAvgMassStatus);
        }
    }
}

void AvgMassManager::clearAvgMassMap()
{
    QMutexLocker locker(&avgMassMapMutex);
    avgMassMap.clear();
    qDebug() << "AvgMassManager: 清空平均质谱数据";
}

bool AvgMassManager::containsAvgMass(const QString &filePath, int eventId)
{
    QMutexLocker locker(&avgMassMapMutex);
    return avgMassMap.contains(filePath) && avgMassMap[filePath].contains(eventId);
}

std::tuple<QVector<double>, QVector<double>, bool, int> AvgMassManager::getAvgMass(const QString &filePath, int eventId)
{
    QMutexLocker locker(&avgMassMapMutex);
    if (avgMassMap.contains(filePath) && avgMassMap[filePath].contains(eventId))
    {
        return avgMassMap[filePath][eventId];
    }
    return std::make_tuple(QVector<double>(), QVector<double>(), false, 0);
}

void AvgMassManager::setAvgMass(const QString &filePath, int eventId,
                                const QVector<double> &xData, const QVector<double> &yData,
                                bool initX, int pointCount)
{
    QMutexLocker locker(&avgMassMapMutex);
    avgMassMap[filePath][eventId] = std::make_tuple(xData, yData, initX, pointCount);

    qDebug() << "AvgMassManager: 设置平均质谱数据，文件:" << filePath
             << ", 事件ID:" << eventId << ", 数据点数:" << xData.size();

    // 发出数据更新信号
    if (s_instance)
    {
        emit s_instance->avgMassDataUpdated(filePath, eventId);
    }
}

void AvgMassManager::TIC2XIC(FileData &data)
{
    qDebug() << "AvgMassManager::TIC2XIC: 开始TIC到XIC的转换";

    // 这里实现TIC2XIC的逻辑
    // 由于这个功能比较复杂，暂时保留空实现
    // 如果需要，可以从原DataReader中移植相关代码

    Q_UNUSED(data);
    qDebug() << "AvgMassManager::TIC2XIC: TIC2XIC功能暂未实现";
}
