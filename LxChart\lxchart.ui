<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LxChart</class>
 <widget class="QWidget" name="LxChart">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1152</width>
    <height>619</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3" rowstretch="0,1">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1">
     <item>
      <widget class="QPushButton" name="btn_YAxsixChange">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>纵坐标切换</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/Percent.svg</normaloff>:/Icons/LxChart/Percent.svg</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_link">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="toolTip">
        <string>关联坐标轴</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/Link.svg</normaloff>:/Icons/LxChart/Link.svg</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_showPeakArea">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="toolTip">
        <string>显示积分区域</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/PeakArea.svg</normaloff>:/Icons/LxChart/PeakArea.svg</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_setBackgroundArea">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>设置背景区域</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/BackgroundArea.svg</normaloff>:/Icons/LxChart/BackgroundArea.svg</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_TIC2XIC">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>从总离子流中提取离子流（XIC）</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/TIC2XIC.png</normaloff>:/Icons/LxChart/TIC2XIC.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_mz2xic">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>由质荷比提取离子流（XIC）</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/mz2XIC.png</normaloff>:/Icons/LxChart/mz2XIC.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_showMassSpec">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>显示质谱图/光谱图</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/showMassSpec.png</normaloff>:/Icons/LxChart/showMassSpec.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_divBaseline">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>基线扣除</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/baseline.png</normaloff>:/Icons/LxChart/baseline.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_lastExperiment">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>向前移动浏览</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/LastExpriment.svg</normaloff>:/Icons/LxChart/LastExpriment.svg</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_nextExperiment">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>向后移动浏览</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/NextExpriment.svg</normaloff>:/Icons/LxChart/NextExpriment.svg</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_splitCharts">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>拆分图谱至独立的图层</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/Split.svg</normaloff>:/Icons/LxChart/Split.svg</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_showAllLayers">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>显示所有图层</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/AllLayer.svg</normaloff>:/Icons/LxChart/AllLayer.svg</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_fullScreen">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>全屏显示当前图层</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/FullScreen.svg</normaloff>:/Icons/LxChart/FullScreen.svg</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_copy">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>重复当前图层至新的图层：在图层下方重复当前图层</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/Copy.svg</normaloff>:/Icons/LxChart/Copy.svg</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_camera">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>复制图层至剪贴板</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../res.qrc">
         <normaloff>:/Icons/LxChart/Camera.svg</normaloff>:/Icons/LxChart/Camera.svg</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <layout class="QGridLayout" name="gridLayout_2" columnstretch="1,5">
     <item row="0" column="1">
      <layout class="QGridLayout" name="gridLayout" rowstretch="0,1">
       <item row="1" column="0">
        <layout class="QGridLayout" name="gridLayout_customChartView"/>
       </item>
       <item row="0" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>当前加载时间:</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item row="0" column="0">
      <widget class="CustomLegendWidget" name="customLegendWidget" native="true">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CustomLegendWidget</class>
   <extends>QWidget</extends>
   <header>customlegendwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../res.qrc"/>
 </resources>
 <connections/>
</ui>
