#include "xicchartdata.h"
#include <QDebug>
#include <QThread>
#include <QtCharts/QLineSeries>
#include <QtCharts/QChart>

XicChartData::XicChartData(QString ParamPath, GlobalEnums::IonMode ionMode, GlobalEnums::ScanMode scanMode, QString dataProcess, QString dataName,
                           QString sampleName, int eventNum, QObject *parent)
    : LxChartData(ParamPath, GlobalEnums::TrackType::XIC, ionMode, scanMode, dataProcess, dataName, sampleName, eventNum, parent)
{
    // XIC特有的初始化代码
}

XicChartData::~XicChartData()
{
    qDebug() << "XicChartData::~XicChartData: 开始析构";

    // 临时完全跳过QLineSeries清理，避免崩溃
    try
    {
        qDebug() << "XicChartData::~XicChartData: 临时跳过QLineSeries清理，让基类处理";
        setSeries(nullptr); // 只设置为nullptr，让基类处理
        qDebug() << "XicChartData::~XicChartData: 析构完成";
        return;
    }
    catch (...)
    {
        qDebug() << "XicChartData::~XicChartData: 设置series为nullptr时异常";
    }

    /*
    // 原来的清理代码
    // 主动清理QLineSeries，避免基类析构时的问题
    try
    {
        QLineSeries *series = nullptr;
        try
        {
            series = getSeries();
        }
        catch (...)
        {
            qDebug() << "XicChartData::~XicChartData: 获取QLineSeries时异常，可能对象已损坏";
            setSeries(nullptr);
            return;
        }

        if (series)
        {
            qDebug() << "XicChartData::~XicChartData: 主动清理QLineSeries，指针:" << static_cast<void *>(series);

            // 原来的清理代码已注释
            */
}
int XicChartData::getTic_event_id() const
{
    return tic_event_id;
}

void XicChartData::setTic_event_id(int newTic_event_id)
{
    tic_event_id = newTic_event_id;
}

bool XicChartData::isIndependent() const
{
    return m_isIndependent;
}

void XicChartData::setIndependent(bool independent)
{
    m_isIndependent = independent;
}
