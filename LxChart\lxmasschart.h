#ifndef LXMASSCHART_H
#define LXMASSCHART_H

#include <QObject>
#include <QMouseEvent>
#include "LxChart/lxchart.h"
#include "Algorithm/PeakFind/wh_peakSearch.h"
#include "Algorithm/PeakFind/peakfind.h"
class LxMassChart : public LxChart
{
    Q_OBJECT
public:
    explicit LxMassChart(GlobalEnums::TrackType type, QWidget *parent = nullptr);

    void getPeakVec(std::vector<Peak> peakVec);

    void connectAll();

protected:
    /**
     * @brief 重写事件过滤器，处理双击事件
     * @param obj 事件对象
     * @param event 事件
     * @return 是否处理了事件
     */
    bool eventFilter(QObject *obj, QEvent *event) override;

private:
    std::vector<Peak> peakVec;

    // 防止在XIC加载过程中重复响应双击
    static bool s_isLoadingXic;

    /**
     * @brief 处理MASS曲线的双击事件（显示所有MASS对应的XIC数据）
     * @param mouseEvent 鼠标事件
     */
    void handleMassDoubleClick(QMouseEvent *mouseEvent);

    /**
     * @brief 为所有MASS曲线显示XIC数据
     * @param dataPos 双击位置的数据坐标
     */
    void showXicChartForAllMass(const QPointF &dataPos);

public:
    /**
     * @brief 重写图例点击处理方法，实现只能有一个图例被选中
     * @param uniqueId 被点击的图例对应的曲线唯一ID
     */
    void handleLegendClicked(const QString &uniqueId) override;

    /**
     * @brief 重写图例同步方法，实现图例和曲线的双向联动
     * @param chartData 曲线数据
     * @param isSelected 是否选中
     */
    void syncLegendSelection(LxChartData *chartData, bool isSelected) override;

private:
    // 图例选中状态管理
    LxChartLegend *m_currentSelectedLegend = nullptr; // 当前选中的图例

signals:
    /**
     * @brief 请求显示多个XIC数据的信号
     * @param xicRequests XIC请求列表，每个元素包含(路径, 事件ID, m/z值)
     */
    void sg_showMultipleXicChart(QVector<std::tuple<QString, int, double>> xicRequests);
};

#endif // LXMASSCHART_H
