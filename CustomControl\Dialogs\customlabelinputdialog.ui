<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CustomLabelInputDialog</class>
 <widget class="QDialog" name="CustomLabelInputDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="1" column="0">
    <widget class="QTextEdit" name="textEdit"/>
   </item>
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="btn_ok">
       <property name="text">
        <string>确认</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_cancel">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="btn_font">
       <property name="minimumSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../../res.qrc">
         <normaloff>:/Icons/QAction/font.png</normaloff>:/Icons/QAction/font.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_color">
       <property name="minimumSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../../res.qrc">
         <normaloff>:/Icons/QAction/hue.png</normaloff>:/Icons/QAction/hue.png</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../res.qrc"/>
 </resources>
 <connections/>
</ui>
