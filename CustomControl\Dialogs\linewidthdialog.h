#ifndef LINEWIDTHDIALOG_H
#define LINEWIDTHDIALOG_H

#include <QDialog>
#include <QAbstractButton>
#include <QDebug>
namespace Ui {
class LineWidthDialog;
}

class LineWidthDialog : public QDialog
{
    Q_OBJECT

public:
    explicit LineWidthDialog(QWidget *parent = nullptr);
    ~LineWidthDialog();
    static int setWidth();
    static int m_int_width;
private slots:
    void on_buttonBox_clicked(QAbstractButton *button);

private:
    Ui::LineWidthDialog *ui;
};

#endif // LINEWIDTHDIALOG_H
