#include "masschartdata.h"

MassChartData::MassChartData(QString ParamPath, GlobalEnums::IonMode ionMode, GlobalEnums::ScanMode scanMode, QString dataProcess, QString dataName,
                             QString sampleName, int eventNum, QObject *parent)
    : LxChartData(ParamPath, GlobalEnums::TrackType::MS, ionMode, scanMode, dataProcess, dataName, sampleName, eventNum, parent)
{
    // Mass特有的初始化代码
}

MassChartData::~MassChartData()
{
    // 清理Mass特有的资源
}

int MassChartData::getTic_event_id() const
{
    return tic_event_id;
}

void MassChartData::setTic_event_id(int newTic_event_id)
{
    tic_event_id = newTic_event_id;
}
