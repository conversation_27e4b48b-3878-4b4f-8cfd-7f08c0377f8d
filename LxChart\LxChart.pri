INCLUDEPATH += $$PWD/../CustomControl
INCLUDEPATH += $$PWD/../CustomControl/Dialogs
FORMS += \
    $$PWD/lxchart.ui \
    $$PWD/lxchartlegend.ui

HEADERS += \
    $$PWD/lxchart.h \
    $$PWD/lxchartdata.h \
    $$PWD/lxchartlegend.h \
    $$PWD/lxmasschart.h \
    $$PWD/lxticxicchart.h \
    $$PWD/masschartdata.h \
    $$PWD/ticchartdata.h \
    $$PWD/xicchartdata.h

SOURCES += \
    $$PWD/lxchart.cpp \
    $$PWD/lxchartdata.cpp \
    $$PWD/lxchartlegend.cpp \
    $$PWD/lxmasschart.cpp \
    $$PWD/lxticxicchart.cpp \
    $$PWD/masschartdata.cpp \
    $$PWD/ticchartdata.cpp \
    $$PWD/xicchartdata.cpp

# QSS样式文件
OTHER_FILES += \
    $$PWD/lxchart_styles.qss

# 将QSS文件复制到输出目录
QMAKE_POST_LINK += $$QMAKE_COPY $$shell_quote($$PWD/lxchart_styles.qss) $$shell_quote($$OUT_PWD/)

