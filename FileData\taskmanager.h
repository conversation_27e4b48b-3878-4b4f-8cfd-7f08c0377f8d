#ifndef TASKMANAGER_H
#define TASKMANAGER_H

#include <QObject>
#include <QThreadPool>
#include <QRunnable>
#include <QDebug>
#include <QThread>
#include <QMutex>
#include <functional>

// 任务类（继承 QRunnable）
class Task : public QRunnable
{
public:
    explicit Task(std::function<void()> func, const QString &taskName = "未命名任务") : m_func(std::move(func)), m_taskName(taskName) {}

    void run() override
    {
        // qDebug() << "任务" << m_taskName << "开始执行，线程:" << QThread::currentThread();
        try
        {
            if (m_func)
            {
                m_func();
            }
        }
        catch (const std::exception &e)
        {
            qDebug() << "任务" << m_taskName << "执行时发生标准异常:" << e.what();
        }
        catch (...)
        {
            qDebug() << "任务" << m_taskName << "执行时发生未知异常";
        }
        // qDebug() << "任务" << m_taskName << "执行完成，线程:" << QThread::currentThread();
    }

private:
    std::function<void()> m_func;
    QString m_taskName;
};

// 线程池管理类 (单例模式)
class TaskManager : public QObject
{
    Q_OBJECT
public:
    // 获取全局唯一实例
    static TaskManager *instance();

    // 析构函数
    ~TaskManager();

    // 设置线程池最大线程数
    void setMaxThreadCount(int count);

    // 获取线程池最大线程数
    int maxThreadCount() const;

    // 获取当前活动线程数
    int activeThreadCount() const;

    // 通用方法，执行任意函数
    template <typename Func, typename... Args>
    void run(Func func, Args &&...args)
    {
        auto boundFunc = std::bind(func, std::forward<Args>(args)...);
        // 获取调用者函数名作为任务名称
        QString taskName = QString("来自%1的任务").arg(__FUNCTION__);
        Task *task = new Task(boundFunc, taskName);
        task->setAutoDelete(true);
        // qDebug() << "提交任务" << taskName << "到线程池，当前线程:" << QThread::currentThread();
        m_threadPool.start(task);
    }

    // 带任务名的版本，便于调试
    template <typename Func, typename... Args>
    void runNamed(const QString &taskName, Func func, Args &&...args)
    {
        auto boundFunc = std::bind(func, std::forward<Args>(args)...);
        Task *task = new Task(boundFunc, taskName);
        task->setAutoDelete(true);
        // qDebug() << "提交任务" << taskName << "到线程池，当前线程:" << QThread::currentThread();
        m_threadPool.start(task);
    }

private:
    // 私有构造函数，防止外部创建实例
    explicit TaskManager(QObject *parent = nullptr);

    // 禁止拷贝和赋值
    TaskManager(const TaskManager &) = delete;
    TaskManager &operator=(const TaskManager &) = delete;

    // 全局唯一实例
    static TaskManager *m_instance;

    // 互斥锁用于保护单例创建
    static QMutex m_mutex;

    // 线程池实例
    QThreadPool m_threadPool;
};

#endif // TASKMANAGER_H
