#include "paramfilereader.h"

ParamFileReader::ParamFileReader(QObject *parent) : QObject(parent)
{
}

ParamFileReader::~ParamFileReader()
{
}

bool ParamFileReader::readFile(const QString &filePath)
{
    m_filePath = filePath;

    // 打开文件
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        qDebug() << "错误: 无法打开.Param文件:" << filePath;
        return false;
    }

    // 读取文件全部内容
    QByteArray fileData = file.readAll();
    file.close();

    qDebug() << "成功读取.Param文件:" << filePath;
    qDebug() << "文件大小:" << fileData.size() << "字节";

    // 解析文件数据
    bool success = parseHeader(fileData) && parseScanConfigs(fileData) && parseTicData(fileData);

    if (success)
    {
        qDebug() << "文件解析成功";
    }
    else
    {
        qDebug() << "文件解析失败";
    }

    return success;
}

QByteArray ParamFileReader::getFileHeader() const
{
    return m_fileHeader;
}

QMap<QString, QMap<QString, QString>> ParamFileReader::getScanConfigs() const
{
    return m_scanConfigs;
}

QVector<qint64> ParamFileReader::getFrameIndices() const
{
    return m_frameIndices;
}

QVector<double> ParamFileReader::getTicTimePoints() const
{
    return m_ticTimePoints;
}

QVector<double> ParamFileReader::getTicIntensities() const
{
    return m_ticIntensities;
}

QString ParamFileReader::getDatFilePath() const
{
    QString datPath = m_filePath;
    if (datPath.contains(".Param"))
    {
        return datPath.replace(".Param", ".Dat");
    }
    else if (datPath.contains(".P"))
    {
        return datPath.replace(".P", ".D");
    }
    return datPath;
}

bool ParamFileReader::parseHeader(const QByteArray &data)
{
    if (data.size() < 100)
    {
        qDebug() << "错误: 文件太小，不是有效的.Param文件";
        return false;
    }

    // 假设文件头部分为前100个字节
    m_headerSize = 100;
    m_fileHeader = data.left(m_headerSize);

    // 打印文件头十六进制内容以供分析
    qDebug() << "文件头十六进制内容:";
    for (int i = 0; i < m_headerSize; i += 16)
    {
        QByteArray line = m_fileHeader.mid(i, 16);
        QString hexLine;
        QString asciiLine;

        for (int j = 0; j < line.size(); ++j)
        {
            unsigned char byte = static_cast<unsigned char>(line.at(j));
            hexLine += QString("%1 ").arg(byte, 2, 16, QChar('0'));
            asciiLine += (byte >= 32 && byte <= 126) ? QChar(byte) : QChar('.');
        }

        // 对齐显示
        while (hexLine.length() < 48)
        {
            hexLine += "   ";
        }

        qDebug() << QString("0x%1: %2 | %3").arg(i, 4, 16, QChar('0')).arg(hexLine).arg(asciiLine);
    }

    // 解析文件头部分，查找配置节偏移量
    // 根据分析，配置节通常以"[Q1Scan]"等节名称开始
    m_configSectionOffset = data.indexOf("[Q1Scan]");
    if (m_configSectionOffset == -1)
    {
        m_configSectionOffset = data.indexOf("[");
    }

    if (m_configSectionOffset == -1)
    {
        qDebug() << "错误: 未找到配置节起始位置";
        return false;
    }

    // 查找配置节结束位置
    int configEndPos = data.indexOf("\0\0\0\0", m_configSectionOffset);
    if (configEndPos == -1)
    {
        qDebug() << "警告: 未找到配置节结束标记，假设其持续到数据结束";
        m_configSectionSize = data.size() - m_configSectionOffset;
    }
    else
    {
        m_configSectionSize = configEndPos - m_configSectionOffset;
    }

    qDebug() << "配置节信息:";
    qDebug() << "  - 偏移量:" << m_configSectionOffset;
    qDebug() << "  - 大小:" << m_configSectionSize;

    // 尝试查找数据段起始位置
    m_dataOffset = configEndPos + 4; // 跳过结束标记
    m_dataSize = data.size() - m_dataOffset;

    qDebug() << "数据段信息:";
    qDebug() << "  - 偏移量:" << m_dataOffset;
    qDebug() << "  - 大小:" << m_dataSize;

    return true;
}

bool ParamFileReader::parseScanConfigs(const QByteArray &data)
{
    if (m_configSectionOffset == -1 || m_configSectionSize <= 0)
    {
        qDebug() << "错误: 配置节未找到或大小无效";
        return false;
    }

    // 提取配置文本
    QByteArray configData = data.mid(m_configSectionOffset, m_configSectionSize);
    QString configText = QString::fromUtf8(configData);

    qDebug() << "配置节原始文本:";
    qDebug() << configText;

    // 按节解析配置
    QStringList sections = configText.split("[", QString::SkipEmptyParts);

    for (const QString &section : sections)
    {
        if (section.isEmpty())
            continue;

        // 节名
        QString sectionName = section.split("]").first();
        if (sectionName.isEmpty())
            continue;

        qDebug() << "解析配置节:" << sectionName;

        // 创建节内键值对映射
        QMap<QString, QString> sectionMap;

        // 获取节内容
        QString sectionContent = section.mid(section.indexOf("]") + 1).trimmed();
        QStringList lines = sectionContent.split("\n", QString::SkipEmptyParts);

        for (const QString &line : lines)
        {
            int equalPos = line.indexOf("=");
            if (equalPos != -1)
            {
                QString key = line.left(equalPos).trimmed();
                QString value = line.mid(equalPos + 1).trimmed();
                sectionMap[key] = value;

                qDebug() << "  - 配置项:" << key << "=" << value;
            }
        }

        // 存储节配置
        m_scanConfigs["[" + sectionName + "]"] = sectionMap;
    }

    qDebug() << "共解析" << m_scanConfigs.size() << "个配置节";
    return !m_scanConfigs.isEmpty();
}

bool ParamFileReader::parseTicData(const QByteArray &data)
{
    if (m_dataOffset == -1 || m_dataSize <= 0)
    {
        qDebug() << "错误: 数据段未找到或大小无效";
        return false;
    }

    qDebug() << "开始解析TIC数据...";

    // 清空现有数据
    m_frameIndices.clear();
    m_ticTimePoints.clear();
    m_ticIntensities.clear();

    // 提取数据段
    QByteArray ticData = data.mid(m_dataOffset, m_dataSize);

    // 分析前64字节的十六进制内容以确定数据结构
    qDebug() << "数据段起始内容(十六进制):";
    for (int i = 0; i < qMin(64, ticData.size()); i += 16)
    {
        QByteArray line = ticData.mid(i, 16);
        QString hexLine;
        QString asciiLine;

        for (int j = 0; j < line.size(); ++j)
        {
            unsigned char byte = static_cast<unsigned char>(line.at(j));
            hexLine += QString("%1 ").arg(byte, 2, 16, QChar('0'));
            asciiLine += (byte >= 32 && byte <= 126) ? QChar(byte) : QChar('.');
        }

        // 对齐显示
        while (hexLine.length() < 48)
        {
            hexLine += "   ";
        }

        qDebug() << QString("0x%1: %2 | %3").arg(i, 4, 16, QChar('0')).arg(hexLine).arg(asciiLine);
    }

    // 基于对文件格式的分析，假设数据结构如下:
    // 1. 首先是帧索引数组
    // 2. 然后是时间点数组
    // 3. 最后是强度数组

    // 按照示例项目标准格式：每24字节代表一个数据点（8字节帧索引 + 8字节时间戳 + 8字节强度）
    int pointCount = ticData.size() / 24;
    if (pointCount == 0)
    {
        qDebug() << "警告: 数据段大小不足以包含完整数据点";
        return false;
    }

    // 尝试解析每个数据点
    for (int i = 0; i < pointCount; i++)
    {
        int offset = i * 24;               // 每个数据点24字节
        if (offset + 23 >= ticData.size()) // 检查是否有完整的24字节
            break;

        // 读取帧索引（文件偏移量）- 按照示例项目标准格式的第一个8字节
        // 根据示例项目文档，每行数据的第一个8字节就是帧索引
        qint64 frameIndex = *reinterpret_cast<const qint64 *>(ticData.constData() + offset);
        if (frameIndex <= 0)
        {
            qDebug() << "警告: 帧索引数据无效，位置:" << i << "，值:" << frameIndex;
            frameIndex = m_dataOffset + (i * 1024); // 使用备用计算方式
        }
        m_frameIndices.append(frameIndex);

        // 读取时间点 (第2个8字节，偏移量+8)
        double timePoint = *reinterpret_cast<const double *>(ticData.constData() + offset + 8);
        if (std::isnan(timePoint) || std::isinf(timePoint))
        {
            qDebug() << "警告: 时间点数据无效，位置:" << i;
            timePoint = 0.0;
        }
        m_ticTimePoints.append(timePoint);

        // 读取强度 (第3个8字节，偏移量+16)
        double intensity = *reinterpret_cast<const double *>(ticData.constData() + offset + 16);
        if (std::isnan(intensity) || std::isinf(intensity))
        {
            qDebug() << "警告: 强度数据无效，位置:" << i;
            intensity = 0.0;
        }
        m_ticIntensities.append(intensity);

        // 打印前10个数据点的信息
        if (i < 10)
        {
            qDebug() << QString("数据点 %1: 帧索引=%2, 时间点=%3, 强度=%4").arg(i).arg(frameIndex).arg(timePoint).arg(intensity);
        }
    }

    qDebug() << "解析TIC数据完成:";
    qDebug() << "  - 帧索引数量:" << m_frameIndices.size();
    qDebug() << "  - 时间点数量:" << m_ticTimePoints.size();
    qDebug() << "  - 强度数量:" << m_ticIntensities.size();

    return !m_ticTimePoints.isEmpty() && !m_ticIntensities.isEmpty();
}

void ParamFileReader::debugPrintAll() const
{
    qDebug() << "\n===== ParamFileReader 数据输出 =====";
    qDebug() << "文件路径:" << m_filePath;
    qDebug() << "对应的Dat文件:" << getDatFilePath();

    qDebug() << "\n文件结构信息:";
    qDebug() << "  - 文件头大小:" << m_headerSize << "字节";
    qDebug() << "  - 配置节偏移量:" << m_configSectionOffset;
    qDebug() << "  - 配置节大小:" << m_configSectionSize << "字节";
    qDebug() << "  - 数据段偏移量:" << m_dataOffset;
    qDebug() << "  - 数据段大小:" << m_dataSize << "字节";

    qDebug() << "\n配置节信息:";
    QMapIterator<QString, QMap<QString, QString>> sectionIt(m_scanConfigs);
    while (sectionIt.hasNext())
    {
        sectionIt.next();
        qDebug() << "节名:" << sectionIt.key();

        QMapIterator<QString, QString> itemIt(sectionIt.value());
        while (itemIt.hasNext())
        {
            itemIt.next();
            qDebug() << "  - " << itemIt.key() << "=" << itemIt.value();
        }
    }

    qDebug() << "\nTIC数据信息:";
    qDebug() << "  - 数据点数量:" << m_ticTimePoints.size();
    qDebug() << "  - 时间范围:" << (m_ticTimePoints.isEmpty() ? 0 : m_ticTimePoints.first()) << "到"
             << (m_ticTimePoints.isEmpty() ? 0 : m_ticTimePoints.last());

    qDebug() << "\n前10个数据点:";
    for (int i = 0; i < qMin(10, m_ticTimePoints.size()); i++)
    {
        qDebug()
            << QString("  数据点 %1: 帧索引=%2, 时间点=%3, 强度=%4").arg(i).arg(m_frameIndices.at(i)).arg(m_ticTimePoints.at(i)).arg(m_ticIntensities.at(i));
    }

    qDebug() << "\n最后10个数据点:";
    for (int i = qMax(0, m_ticTimePoints.size() - 10); i < m_ticTimePoints.size(); i++)
    {
        qDebug()
            << QString("  数据点 %1: 帧索引=%2, 时间点=%3, 强度=%4").arg(i).arg(m_frameIndices.at(i)).arg(m_ticTimePoints.at(i)).arg(m_ticIntensities.at(i));
    }

    qDebug() << "===== ParamFileReader 数据输出结束 =====\n";
}
