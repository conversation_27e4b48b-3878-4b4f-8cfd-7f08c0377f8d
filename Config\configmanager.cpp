#include "configmanager.h"
// 静态成员变量初始化
QString ConfigManager::m_filePath;
QDomDocument ConfigManager::m_doc;
bool ConfigManager::isFirstInit = false;
ConfigManager &ConfigManager::getInstance()
{
    static ConfigManager instance; // 饿汉模式
    return instance;
}

bool ConfigManager::saveConfig()
{
    QFile file(m_filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
        qDebug() << "无法保存配置文件";
        return false;
    }
    QTextStream stream(&file);
    stream << m_doc.toString();
    file.close();
    return true;
}

QString ConfigManager::setProperty(const QString &className, const QString &propertyName, const QString &value)
{
    QDomElement root = m_doc.documentElement();
    QDomNodeList classList = root.elementsByTagName("class");
    QDomElement classElement;
    bool classExists = false;

    // 检查类是否存在
    for (int i = 0; i < classList.size(); ++i) {
        classElement = classList.at(i).toElement();
        if (classElement.attribute("name") == className) {
            classExists = true;
            break; // 找到类后可以退出循环
        }
    }

    bool propertyExists = false;
    // 如果类不存在，创建新的类
    if (!classExists) {
        classElement = m_doc.createElement("class");
        classElement.setAttribute("name", className);
        root.appendChild(classElement); // 将新类添加到根元素
    } else {
        // 现在确保类存在，检查属性
        QDomNodeList propertyList = classElement.elementsByTagName("property");

        for (int j = 0; j < propertyList.size(); ++j) {
            QDomElement propertyElement = propertyList.at(j).toElement();
            if (propertyElement.attribute("name") == propertyName) {
                // 如果属性存在，修改其值
                QDomNode valueNode = propertyElement.firstChildElement("value");
                valueNode.firstChild().setNodeValue(value);
                propertyExists = true;
                break; // 找到属性后可以退出循环
            }
        }
    }
    // 如果属性不存在，添加新的属性
    if (!propertyExists) {
        QDomElement propertyElement = m_doc.createElement("property");
        propertyElement.setAttribute("name", propertyName);

        QDomElement valueElement = m_doc.createElement("value");
        valueElement.appendChild(m_doc.createTextNode(value));
        propertyElement.appendChild(valueElement);

        classElement.appendChild(propertyElement);
        qDebug() << "添加属性";
    }

    // 保存配置
    if (saveConfig()) {
        return value;
    }
    return "";
}

bool ConfigManager::removeProperty(const QString &className, const QString &propertyName)
{
    QDomElement root = m_doc.documentElement();
    QDomNodeList classList = root.elementsByTagName("class");

    for (int i = 0; i < classList.size(); ++i) {
        QDomElement classElement = classList.at(i).toElement();
        if (classElement.attribute("name") == className) {
            QDomNodeList propertyList = classElement.elementsByTagName("property");
            for (int j = 0; j < propertyList.size(); ++j) {
                QDomElement propertyElement = propertyList.at(j).toElement();
                if (propertyElement.attribute("name") == propertyName) {
                    classElement.removeChild(propertyElement);
                    if (saveConfig()) {
                        return true;
                    }
                    return false;
                }
            }
        }
    }
    qDebug() << "类名或属性名未找到";
}

QString ConfigManager::getPropertyValue(const QString &className, const QString &propertyName)
{
    QDomElement root = m_doc.documentElement();
    QDomNodeList classList = root.elementsByTagName("class");

    for (int i = 0; i < classList.size(); ++i) {
        QDomElement classElement = classList.at(i).toElement();
        if (classElement.attribute("name") == className) {
            QDomNodeList propertyList = classElement.elementsByTagName("property");
            for (int j = 0; j < propertyList.size(); ++j) {
                QDomElement propertyElement = propertyList.at(j).toElement();
                if (propertyElement.attribute("name") == propertyName) {
                    QDomNode valueNode = propertyElement.firstChildElement("value");
                    return valueNode.firstChild().nodeValue();
                }
            }
        }
    }
    qDebug() << "类名或属性名未找到";
    return QString();
}

void ConfigManager::setConfigFilePath(QString path)
{
    m_filePath = path;
}

ConfigManager::ConfigManager()
{
}

bool ConfigManager::loadConfig()
{
}
