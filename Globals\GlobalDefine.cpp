#include "GlobalDefine.h"
#include <QSettings>
#include <QCoreApplication>

// 全局分析配置管理器实现
GlobalDefine::AnalysisConfig GlobalDefine::GlobalAnalysisConfig::m_config;

GlobalDefine::GlobalAnalysisConfig &GlobalDefine::GlobalAnalysisConfig::getInstance()
{
    static GlobalAnalysisConfig instance;
    return instance;
}

GlobalDefine::AnalysisConfig *GlobalDefine::GlobalAnalysisConfig::getConfig()
{
    // 确保单例已初始化
    getInstance();
    return &m_config;
}

void GlobalDefine::GlobalAnalysisConfig::loadConfigFile()
{
    // 完全按照示例项目sDataAnalysis::loadConfigFile的方式
    QString configPath = QCoreApplication::applicationDirPath() + "/system.ini";
    QSettings configIniRead(configPath, QSettings::IniFormat);

    // 完全按照示例项目的代码：
    // pCONGIG_ANALYSIS->Period = configIniRead.value("/Analysis/Period", 86400).toDouble();
    // 但是我们使用86400000作为默认值以支持大文件
    m_config.Period = configIniRead.value("/Analysis/Period", 86400000).toDouble();
    m_config.maxiHeighMassChart = configIniRead.value("/Analysis/maxiHeighMassChart", 16777215).toInt();

    // qDebug() << "=== 全局分析配置加载完成 ===";
    // qDebug() << "配置文件路径:" << configPath;
    // qDebug() << "Period值:" << m_config.Period << "秒（" << (m_config.Period / 86400.0) << "天）";
    // qDebug() << "maxiHeighMassChart:" << m_config.maxiHeighMassChart;
}

GlobalDefine::GlobalPaths &GlobalDefine::GlobalPaths::getInstance()
{
    static GlobalPaths instance; // 饿汉模式
    return instance;
}

QString GlobalDefine::GlobalPaths::getExePath()
{
    return QCoreApplication::applicationDirPath(); // 获取exe程序路径
}

QString GlobalDefine::GlobalPaths::getConfigDir()
{
    return getExePath() + "/config"; // 获取配置文件目录
}

// 移除了PeakFindingConfig.xml相关方法
