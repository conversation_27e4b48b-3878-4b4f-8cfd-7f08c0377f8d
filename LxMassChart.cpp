void LxMassChart::connectAll()
{
    connect(&PeakFind::getInstance(), &PeakFind::sg_searchPeaksSuccess, this, [=](QString windowId) {
        if (windowId == this->getWindowId()) {
            foreach (LxChartData *data, m_chartDataVec) {
                if (!data->getHasFindPeak()) {
                    // 设置所有数据为已寻峰
                    data->setHasFindPeak(true);
                }
                qDebug() << data->getParamPath() << "峰数量:" << data->peakVec.size();
                // foreach (Peak p, data->peakVec) {
                //     qDebug() << "Peak area:" << p.area << " start:" << p.start << " end:" << p.end << " fwhm:" << p.fwhm << " height:" << p.height
                //              << " top:" << p.top;
                // }
            }
            showPeaks();
        }
    }, Qt::QueuedConnection);
} 