#include "linewidthdialog.h"
#include "ui_linewidthdialog.h"
int LineWidthDialog::m_int_width = -1;
LineWidthDialog::LineWidthDialog(QWidget *parent) : QDialog(parent), ui(new Ui::LineWidthDialog)
{
    ui->setupUi(this);
    setModal(true);
}

LineWidthDialog::~LineWidthDialog()
{
    delete ui;
}

void LineWidthDialog::on_buttonBox_clicked(QAbstractButton *button)
{
    QDialogButtonBox::ButtonRole role = ui->buttonBox->buttonRole(button);
    if (role == QDialogButtonBox::AcceptRole) {
        m_int_width = ui->spinBox_width->value();
    }
}

int LineWidthDialog::setWidth()
{
    LineWidthDialog widthDialog;
    widthDialog.exec();
    return m_int_width;
}
