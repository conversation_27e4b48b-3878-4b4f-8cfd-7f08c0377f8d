# 三重四极杆质谱仪数据分析软件设计文档

## 1. 项目概述

本项目是一个用于三重四极杆质谱仪数据分析的软件，基于QT和C++开发。软件主要功能包括读取Param和Dat文件，分析和处理质谱数据，并进行可视化展示。

## 2. 软件流程图

```
┌─────────────────┐        ┌─────────────────┐        ┌─────────────────┐
│                 │        │                 │        │                 │
│    读取Param    │───────>│    读取Dat     │───────>│    数据处理     │
│     文件        │        │    文件        │        │                 │
│                 │        │                 │        │                 │
└─────────────────┘        └─────────────────┘        └─────────────────┘
                                                              │
                                                              │
                                                              ▼
┌─────────────────┐        ┌─────────────────┐        ┌─────────────────┐
│                 │        │                 │        │                 │
│    MRM数据     │<───────│    扫描类型     │<───────│    分析数据     │
│    展示        │        │    判断        │        │                 │
│                 │        │                 │        │                 │
└─────────────────┘        └─────────────────┘        └─────────────────┘
```

## 3. 数据流向

```
Param文件 ──> FileData对象 ──> 解析Segment和Event ──> 获取扫描类型
                │
                ▼
Dat文件  ──> 解析二进制数据 ──> TIC/XIC/Mass数据 ──> LxChartData ──> 可视化展示
                │
                ▼
            MRM数据处理 ──> StructMRM向量 ──> MRM表格显示
```

## 4. 最近修改内容

### 4.1 曲线颜色管理系统

为了提升用户体验和数据可视化效果，我们实现了一套完整的曲线颜色管理系统：

1. **随机颜色生成**：
   - 实现了随机颜色生成函数，只随机绿色和蓝色分量
   - 红色分量保持为0，避免与高亮颜色（红色）混淆
   - 确保生成的颜色不会重复

2. **颜色存储与复用**：
   - 在LxChart类中添加QMap<QString, QString>用于存储十六进制颜色值
   - 使用LxChartData的UniqueID作为键
   - 新增QStack<QString> freeColorStack用于存储被释放的颜色，实现颜色复用
   - 添加曲线时优先从freeColorStack中获取已释放的颜色，仅在freeColorStack为空时才生成新的随机颜色

3. **颜色属性扩展**：
   - 在LxChartData中添加原始颜色属性，用于高亮后恢复
   - 添加了相应的getter和setter方法

4. **曲线高亮功能**：
   - 实现最近点所在曲线的高亮显示，使用红色
   - 鼠标移出时自动恢复原始颜色
   - 使用数学公式计算点到直线的距离，并采用像素单位进行判断

5. **颜色生命周期管理**：
   - 在添加曲线时生成并设置随机颜色或使用已释放的颜色
   - 在移除曲线时将颜色存入freeColorStack栈中，便于后续复用
   - 在清空所有曲线时，先将所有颜色保存到freeColorStack中，再清空颜色映射表

6. **曲线和最近点查找逻辑**：
   - 使用findIndex函数查找x轴最近的点索引
   - 基于LxQwtPlot的实现参考，对mouseMoveEvent函数进行全面重构
   - 实现了基于边界框的筛选逻辑，更精准地识别合格点
   - 当找到多个合格点时，可以智能选择最近的点进行高亮显示

### 4.2 MRM数据处理优化

MRM数据处理逻辑进行了优化：

1. **更精确的数据识别**：
   - 添加无效值检测和过滤
   - 支持手动定义特定筛选规则

2. **多通道数据处理**：
   - 支持处理所有通道的数据
   - 通过遍历mass、massPre等数组获取有效数据

### 4.3 背景区域绘制优化

为了解决在调整坐标轴范围后背景区域显示不正确的问题，我们对背景区域绘制逻辑进行了优化：

1. **背景区域数据范围持久化**：
   - 添加了`QPair<qreal, qreal> m_backgroundAreaRange`成员变量，用于存储背景区域的实际X轴范围
   - 在`handleBackgroundAreaSelection`函数中保存背景区域的真实数据范围
   - 确保在缩放和其他坐标轴调整操作后，背景区域的数据范围保持不变

2. **自动重绘背景区域**：
   - 修改了`SetAxisScale()`和`handleZoom()`函数，在坐标轴范围改变后重新绘制背景区域
   - 重绘时使用保存的数据范围，而不是从当前UI元素中计算范围
   - 确保背景区域在任何缩放级别下都能正确显示，并且数据范围不会随视图调整而改变

3. **背景区域范围验证**：
   - 添加了对背景区域范围有效性的检查，只有当范围有效（不是0,0）时才进行重绘
   - 在清除背景区域时同时重置保存的范围数据

4. **异常处理与调试**：
   - 添加对`defaultSeries`的空值检查，防止在缺少默认曲线的情况下调用`handleBackgroundAreaSelection`函数导致崩溃
   - 添加了详细的调试信息，区分不同方法中的背景区域重绘操作，便于追踪背景区域绘制过程

### 4.4 背景区域X轴范围限制

为了解决背景区域在X轴方向超出坐标轴范围的问题，对背景区域绘制逻辑进行了进一步优化：

1. **X轴范围检查与限制**：
   - 在`handleBackgroundAreaSelection`函数中添加了对X轴当前范围的获取
   - 使用`qMax`和`qMin`函数确保背景区域的起点和终点不超出坐标轴的当前范围
   - 确保背景区域始终在坐标系可见区域内，避免绘制到坐标轴外部

2. **坐标轴缩放时的范围维护**：
   - 优化了`SetAxisScale()`函数中的背景区域重绘逻辑
   - 在重新计算坐标轴范围后，检查并调整背景区域范围，确保其不超出新的坐标轴范围
   - 使用调整后的范围重新绘制背景区域，保证显示效果的一致性

3. **调试信息完善**：
   - 添加了更详细的调试输出，包括原始背景区域范围和调整后的范围
   - 显示坐标轴当前范围，便于追踪背景区域绘制过程和问题诊断

### 4.5 背景区域动态调整功能

为了提升用户交互体验，我们实现了背景区域的动态调整功能：

1. **拖拽边界调整功能**：
   - 鼠标移动到背景区域两侧边缘时，光标会变成双箭头（水平调整形状）
   - 用户可以按住左键拖动背景区域的左右边界，实现动态调整背景区域范围
   - 在拖动过程中实时预览背景区域的新位置，但只有在鼠标释放后才最终确认并更新背景区域

2. **边界限制**：
   - 背景区域的边界不能超出当前坐标轴的显示范围
   - 左侧边界不能向右超过右侧边界，右侧边界不能向左超过左侧边界
   - 这确保了背景区域的有效性和一致性

3. **视觉反馈**：
   - 鼠标悬停在边界区域时更改光标形状，提供直观的视觉提示
   - 拖动过程中实时更新背景区域的显示，提供即时视觉反馈

4. **实现细节**：
   - 使用临时背景区域变量存储拖动过程中的区域状态
   - 添加拖动状态标志以跟踪当前是否正在拖动左边界或右边界
   - 在鼠标释放时才调用handleBackgroundAreaSelection更新实际背景区域

5. **交互优化**：
   - 当鼠标位于背景区域边缘或进行拖动操作时，自动隐藏十字准星，避免干扰用户操作
   - 拖动时显示实时边界值的信息气泡，帮助用户精确定位
   - 气泡提示框始终显示在鼠标光标右上方，确保不遮挡拖动操作
   - 拖动过程中强制使用双箭头光标，提供稳定的视觉反馈

6. **边界虚线标记**：
   - 背景区域的左右边界使用蓝色虚线进行标记，更加醒目且符合常规应用界面风格
   - 鼠标靠近边界虚线时自动变为双箭头形状，提示用户可以进行拖动调整
   - 边界检测不仅基于矩形边缘，还包括虚线边界，提高了交互的精确度
   - 在缩放和调整坐标轴范围时，边界虚线会随背景区域一起更新位置

### 4.6 缩放与区域同步刷新优化

为了解决图表缩放时区域选择和背景区域不同步刷新的问题，我们优化了相关处理逻辑：

1. **缩放时区域同步刷新**：
   - 修改了`handleZoom`函数，使其在缩放时同时处理普通区域和背景区域的刷新
   - 确保区域范围在新的坐标轴范围内有效，避免超出可见范围
   - 使用`qMax`和`qMin`函数调整区域范围，保证视觉效果的连续性

2. **坐标轴范围变化时的处理优化**：
   - 保存当前坐标轴范围，便于后续更新各类区域
   - 对普通区域和背景区域使用相同的范围限制逻辑，保证一致性
   - 增加了范围有效性检查，只有当区域真实存在时才进行刷新

3. **视觉显示一致性**：
   - 确保在各种缩放级别下，区域选择和背景区域的视觉效果保持一致
   - 在缩放操作后强制刷新图表，确保所有变更立即可见

### 4.7 右键菜单优化

为了提升用户交互体验，我们对右键菜单功能进行了优化：

1. **重叠区域菜单处理**：
   - 在普通区域和背景区域重叠部分右击时，显示包含两个删除选项的菜单
   - 分别提供"删除区域选择"和"删除背景区域"选项，让用户可以选择需要删除的区域
   - 使用连接槽函数的方式优化菜单逻辑，提高代码清晰度

2. **菜单项动态显示**：
   - 根据点击位置判断是否显示相应的菜单项
   - 只有在点击了特定区域时才显示相应的删除选项，避免无效操作
   - 使用布尔变量跟踪菜单项状态，确保菜单内容的正确性

3. **交互简化**：
   - 移除了MouseButtonRelease事件中重复的右键菜单处理代码
   - 在MouseButtonPress事件中统一处理右键菜单逻辑，避免冗余操作
   - 确保菜单显示位置准确，提供良好的用户体验

4. **视觉反馈**：
   - 点击菜单项后立即执行相应的清除操作，提供即时的视觉反馈
   - 在删除操作后刷新图表，确保UI状态与内部数据一致

这些优化使得用户可以更直观、更精确地控制区域选择和背景区域，同时在缩放操作后保持视觉效果的一致性，大大提升了软件的易用性和用户体验。

### 4.8 区域边界统一样式

为了提高界面的一致性和美观性，我们对背景区域和自定义区域的边界线进行了统一样式处理：

1. **边界线样式统一**：
   - 将背景区域和自定义区域的边界线统一修改为灰色实线（Qt::gray, Qt::SolidLine）
   - 替换了原有的蓝色虚线和绿色虚线样式
   - 保留了不同区域的填充色差异，以便区分背景区域（粉红色）和自定义区域（绿色）

2. **鼠标交互优化**：
   - 当鼠标悬停在边界区域（±5像素范围内）时，自动切换为水平调整光标（Qt::SizeHorCursor）
   - 离开边界区域时恢复为十字准星样式
   - 按下左键拖动时保持水平调整光标样式，直到拖动完成

3. **边界区域拖动行为**：
   - 支持拖动修改区域边界，实时动态显示区域大小变化
   - 防止左右边界交叉，确保区域始终有效
   - 当左右边界重合时自动删除区域

4. **实现细节**：
   - 修改了`handleBackgroundAreaSelection`和`handleRegionSelection`函数中的边界线样式设置
   - 确保在`updateTempBackgroundArea`中保持统一的视觉样式
   - 保留了原有的边界检测和交互逻辑，仅更新了视觉呈现方式

这些修改提高了软件界面的一致性和专业性，使得区域边界线更加清晰可辨，同时保持了不同区域类型的视觉区分，提升了整体用户体验。

### 4.9 区域边界交互修复

为了解决区域边界交互中的几个问题，我们进行了以下优化：

1. **缩放时背景区域保持原始范围**：
   - 修复了图表缩放时背景区域范围被错误修改的问题
   - 将`SetAxisScale`和`handleZoom`中的`handleBackgroundAreaSelection`调用替换为`updateTempBackgroundArea`
   - 保留原始背景区域范围，只更新视觉显示，确保在任何缩放级别下都不改变用户设置的背景区域实际范围
   - 在可视区域外的背景区域不显示，但原始范围数据保持不变

2. **光标样式交互优化**：
   - 修复了鼠标悬停在边界区域时未能正确显示为`SizeHorCursor`的问题
   - 改进了边界检测逻辑，确保在靠近边界±5像素范围内正确识别
   - 在`MouseMove`事件中增加了对边界检测的优先级处理

3. **拖动时光标样式保持**：
   - 修复了按住左键拖动时光标变回传统箭头的问题
   - 在`handleBgEdgeDragging`中强制设置光标为`SizeHorCursor`
   - 在`MouseMove`事件处理中确保拖动状态下始终保持双箭头样式

### 4.10 区域显示和缩放BUG修复

为了解决区域显示和图表缩放相关的几个关键问题，我们进行了以下优化：

1. **曲线移除后区域显示问题修复**：
   - 修复了移除范围较大的曲线后，坐标轴自动刷新，但自定义区域和背景区域不刷新的问题
   - 在`RemoveLxChartDataByUniqueID`函数中添加了对背景区域的清除处理
   - 增加对默认曲线的重新初始化，确保曲线移除后相关区域能正确显示
   - 确保在移除曲线导致的坐标轴范围变化时，区域能根据新范围正确更新

2. **多次缩放功能修复**：
   - 修复了放大区域只能放大一次的问题
   - 在`handleZoom`函数中增加了对defaultSeries的检查和重新获取
   - 对不存在的区域元素（矩形、边界线）进行创建操作，确保多次缩放时区域元素不丢失
   - 添加了对区域范围有效性的检查，避免无效区域的绘制

3. **缩放操作后区域恢复问题**：
   - 确保在`resetZoom`函数中正确重建背景区域和自定义区域
   - 新增对背景区域图形元素是否存在的检查，不存在时创建
   - 添加坐标轴范围检查，确保区域范围在当前坐标轴范围内有效
   - 强制在缩放操作后刷新图表，确保所有变更立即可见

4. **SetAxisScale函数优化**：
   - 增强了`SetAxisScale`函数的鲁棒性，添加对defaultSeries为空的处理
   - 增加了对背景区域和自定义区域范围有效性的验证，只有在有效范围内才进行绘制
   - 在更新自定义区域失败时添加了明确的错误提示，便于调试
   - 优化了区域范围调整逻辑，确保不会产生无效的区域

这些修复解决了在复杂图表操作场景（如曲线添加/删除、多次缩放、范围调整等）下出现的区域显示和交互问题，提高了软件的稳定性和用户体验。

### 4.11 自定义标注控件事件处理优化

为了解决自定义标注控件的鼠标事件被`LxChart`拦截的问题，我们实现了一套完整的事件处理机制：

1. **自定义代理控件实现**：
   - 创建了继承自`QGraphicsProxyWidget`的`CustomProxyWidget`类
   - 重写了`sceneEvent`方法，根据事件类型分发到特定的处理函数
   - 实现了对鼠标按下、释放、移动和双击事件的专门处理
   - 设置较高的Z值（100），确保代理控件能优先接收事件

2. **事件转发机制**：
   - 实现了`sendMouseEventToWidget`方法，将场景事件转换为控件事件
   - 将场景坐标正确映射到控件坐标系统
   - 创建新的鼠标事件并发送给嵌入的`CustomLabelWidget`控件
   - 根据控件的事件处理结果决定是否继续传递事件

3. **标签控件鼠标事件处理**：
   - 在`CustomLabelWidget`中重写了`mousePressEvent`方法
   - 实现了对右键点击的特别处理，显示上下文菜单
   - 提供了编辑和删除标签的操作选项
   - 确保鼠标事件被正确处理并不再传递给图表控件

4. **与图表控件的集成**：
   - 修改了`LxChart::CreateCustomLabel`函数，使用`CustomProxyWidget`替代原来的`QGraphicsProxyWidget`
   - 确保标签位置计算逻辑与新的代理控件兼容
   - 优化了标签的显示逻辑，确保标签在图表区域内可见

5. **标签位置动态更新**：
   - 在图表缩放和调整时自动更新标签位置
   - 实现了标签位置的边界检查，确保标签不会超出图表范围
   - 当右侧空间不足时，自动将标签显示在左侧

6. **调试支持**：
   - 添加了详细的调试输出，用于跟踪事件传递过程
   - 记录标签创建、更新和事件处理的关键信息
   - 便于开发者理解和排查事件处理相关问题

这一优化确保了自定义标注控件能够正确接收和处理鼠标事件，而不被`LxChart`控件拦截。用户现在可以直接在标签上点击右键进行操作，提供了更直观和符合预期的交互体验。

### 4.12 峰阴影区域显示功能

为了提升峰检测结果的可视化效果，我们实现了峰阴影区域显示功能：

1. **峰结构体扩展**：
   - 在 `Peak` 结构体中添加了 `shadeItem` 字段，用于存储峰的阴影区域图形项
   - 类型为 `QGraphicsPathItem*`，可以绘制任意形状的阴影区域
   - 与现有的 `item` 字段（标记峰顶点的小圆点）协同工作

2. **阴影区域绘制**：
   - 使用 `QPainterPath` 创建一个封闭路径，包含峰的所有数据点和基线
   - 基线连接峰的起点 (`pStart`) 和终点 (`pEnd`)，形成封闭区域
   - 使用交替的颜色（灰色和黑色）为相邻的峰填充不同的阴影颜色，提高可识别性
   - 设置半透明填充色，避免完全遮挡底层图表

3. **显示与隐藏机制**：
   - 在 `showPeaks()` 函数中创建阴影区域和峰顶点标记
   - 在 `hidePeaks()` 函数中清除所有阴影区域和标记点
   - 在 `updatePeaksPos()` 函数中更新阴影区域和标记点的位置，确保它们与图表视图保持同步

4. **可见性优化**：
   - 根据当前图表的可视范围动态显示或隐藏阴影区域
   - 当峰的任一特征点（顶点、起点、终点）在可视范围内时，显示阴影区域
   - 否则隐藏阴影区域，提高渲染性能

5. **颜色可配置性**：
   - 添加 `setPeakShadeColors()` 函数，允许用户自定义阴影区域的颜色
   - 默认使用半透明的灰色和黑色，可根据需要进行调整
   - 颜色交替应用于连续的峰，提高视觉区分度

6. **防止重复添加**：
   - 实现了检查图形项是否已在场景中的逻辑
   - 在添加到场景前先移除，避免 "item has already been added to this scene" 警告
   - 确保图形项的正确释放，防止内存泄漏

这些改进使得峰检测结果的显示更加直观和易于理解，用户可以清晰地看到每个峰的范围和形状，提升了数据分析的效率和准确性。

## 5. 待改进项

1. **线程池实现**：
   - 需要添加线程池支持，优化数据读取和处理性能
   - 避免在大数据量处理时UI卡顿

2. **MRM数据筛选优化**：
   - 进一步完善对无效值和异常值的处理
   - 添加高级过滤选项

3. **UI交互优化**：
   - 优化曲线选择和高亮逻辑
   - 改进距离计算算法，提高精度
   - 完善背景区域与其他绘图元素的交互，如处理背景区域与高亮点的配合显示

## 6. 控件说明

| 控件名称 | 来源 | 作用 |
|---------|------|-----|
| LxChart | 自定义 | 基于QChart的图表控件，用于显示质谱数据 |
| LxChartData | 自定义 | 存储和管理图表数据，与曲线关联 |
| MRMReader | 自定义 | 读取和处理MRM数据 |
| FileData | 自定义 | 存储和管理从Param和Dat文件中读取的数据 |
| FileDataManager | 自定义 | 管理所有FileData对象 |
| CustomProxyWidget | 自定义 | 代理控件，用于处理自定义标注控件的事件 |
| CustomLabelWidget | 自定义 | 标注控件，用于在图表上显示自定义标签 |


## 7.遗留问题待解决
1.当前正在设计多事件支持，但目前没有多事件文件进行读取测试，也无法正常读取多事件参数及每个事件对应的扫描类型和数据。所以暂停多事件支持的设计，转为继续设计人工标注功能。
   此时多事件支持有下面几个问题需要思考和解决：
   - 当前一个TIC曲线表示一个TIC文件内的某个事件，双击选取某帧后要对所有TIC曲线进行加载MASS数据，
   LxChart::sg_chartDoubleClicked
   但当前加载MASS数据的函数是以TIC文件为单位加载的，如果有多个TIC曲线属于同一个TIC文件，那么加载的MASS数据就是一样的，这样可能会丢失真实的MASS数据，因为每次同一TIC文件下的事件对应的MASS数据并不都是一样的。
   
   - 当前任何曲线都是LxChartData*类型，这个类表示图表曲线，此类有一个唯一ID，也有一个ParamPath作为找到从属TIC文件的唯一索引。

   - 目前需要解决的是读取一个TIC数据文件里的所有数据并解析：
       所有TIC事件
       TIC事件对应的TIC数据
       TIC事件对应的MASS数据的读取
       TIC事件对应的扫描类型

   - 能够获取所有数据之后再优化数据结构

   - 和周旭沟通开放dataDisassembleFirst函数MRM事件类型解析数据