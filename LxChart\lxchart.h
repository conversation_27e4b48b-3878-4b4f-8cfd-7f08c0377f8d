#ifndef LXCHART_H
#define LXCHART_H

#include <QWidget>
#include <QtCharts/QtCharts>
#include <QGraphicsSceneMouseEvent>
#include <QVector>
#include <QPointF>
#include <limits>
#include <QMenu>
#include <QAction>
#include <QLabel>
#include <QPixmap>
#include <QPainter>
#include "CustomControl/Dialogs/customlabelwidget.h"
#include <QUuid>
#include <QMap>
#include <QStack>
#include <QList>
#include "Globals/GlobalEnums.h"
#include "lxchartdata.h"
#include <QMouseEvent>
#include "FileData/filedatamanager.h"
#include "Globals/GlobalDefine.h"
#include "CustomControl/Dialogs/customlegendwidget.h"
#include <QLineF>
#include "CustomControl/Dialogs/customlabelinputdialog.h"
// QT_CHARTS_USE_NAMESPACE;

class CustomChartView : public QChartView
{
public:
    CustomChartView(QWidget *parent = nullptr) : QChartView(parent) {}
    bool m_bool_enableUpdate = false;
    double diff = 1;
    double minY = 0;
    QColor defaultColor;
    void clearLables()
    {
        // 清除之前绘制的标签
        qDeleteAll(customLabels);
        customLabels.clear();
    };

protected:
    void paintEvent(QPaintEvent *event) override
    {
        QChartView::paintEvent(event);
        if (!m_bool_enableUpdate)
        {
            return;
        }
        updateCustomLabels(); // 每次重绘时更新自定义标签
    }

private:
    QList<QGraphicsTextItem *> customLabels; // 存储自定义标签，方便删除
    void updateCustomLabels()
    {
        // 清除之前绘制的标签
        clearLables();

        // 获取Y轴
        QList<QAbstractAxis *> axes = chart()->axes(Qt::Vertical);
        if (axes.isEmpty())
        {
            qDebug() << "No Y axis found!";
            return;
        }

        QValueAxis *axisY = qobject_cast<QValueAxis *>(axes.first());
        if (!axisY)
        {
            return;
        }

        // 获取 X 轴
        QList<QAbstractAxis *> xAxes = chart()->axes(Qt::Horizontal);
        if (xAxes.isEmpty())
        {
            return;
        }

        // 获取 X 轴的最小值（假设是 QValueAxis）
        QValueAxis *axisX = qobject_cast<QValueAxis *>(xAxes.first());
        if (!axisX)
        {
            return;
        }

        qreal minX = axisX->min(); // 获取 X 轴的最小值

        // 获取原Y轴的刻度范围
        qreal min = axisY->min();
        qreal max = axisY->max();

        // 获取原Y轴的字体
        QFont font = axisY->labelsFont();

        // 获取刻度数量
        int tickCount = axisY->tickCount();

        // 计算每个刻度的间隔值
        qreal tickInterval = (max - min) / (tickCount - 1);

        // **获取 Y 轴在图表中的 X 位置**
        QPointF yAxisPos = chart()->mapToPosition(QPointF(minX, min));

        for (int i = 0; i < tickCount; ++i)
        {
            qreal value = min + i * tickInterval; // 计算刻度值

            // 获取原刻度在图表中的像素位置
            QPointF pos = chart()->mapToPosition(QPointF(minX, value));

            // **将 `x` 位置设为 `yAxisPos.x()`，确保文本在 Y 轴附近**
            QPointF fixedPos(yAxisPos.x(), pos.y());
            if (diff == 0)
            {
                diff = 1;
            }
            double percent = ((value - minY) / diff) * 100;
            // 创建新标签（原值 / 100）
            QGraphicsTextItem *newLabel = new QGraphicsTextItem(QString::number(percent, 'f', 2) + "%");
            newLabel->setFont(font);
            newLabel->setDefaultTextColor(defaultColor); // 设置颜色（可自定义）

            // 获取文本的大小，并调整绘制位置（确保底部对齐）
            QRectF textRect = newLabel->boundingRect();
            QPointF newPos(fixedPos.x() - textRect.width(), fixedPos.y() - textRect.height() / 2);
            newLabel->setPos(newPos); // 右侧 30px

            // 将新标签添加到 chart 的场景中
            chart()->scene()->addItem(newLabel);
            customLabels.append(newLabel); // 存储以便删除
        }

        m_bool_enableUpdate = false;
    }
};
namespace Ui
{
    class LxChart;
}

class LxChart : public QWidget
{
    Q_OBJECT

public:
    explicit LxChart(GlobalEnums::TrackType type, QWidget *parent = nullptr);
    explicit LxChart(const QList<GlobalEnums::TrackType> &allowedTypes, QWidget *parent = nullptr);
    ~LxChart();

    // 重置图表到原始状态
    void resetZoom();
    // 重置X轴到原始范围
    void resetXAxis();
    // 重置Y轴到原始范围
    void resetYAxis();

    // 获取内部QChart对象
    QChart *chart() const { return m_chart; }
    // 获取窗口ID
    QString getWindowId() const { return m_windowId; }
    // 是否是拆分窗口
    bool isSplitWindow() const { return m_isSplitWindow; }
    // 设置为拆分窗口
    void setSplitWindow(bool isSplit);

    // 添加、更新或获取曲线，确保所有曲线都使用统一的线宽设置
    QLineSeries *getSeries(const QString &name, bool createIfNotExists = true, const QColor &color = QColor());
    // 获取默认数据曲线
    QAbstractSeries *getDefaultSeries();

    // 计算所有曲线的数据范围
    void calculateDataRange();

    // 设置是否在显示十字准星时隐藏鼠标光标
    void setHideCursorWithCrosshair(bool hide);

    // 关闭所有子窗口
    static void closeAllSplitWindows();

    // 获取所有拆分窗口的ID列表
    static QList<QString> getAllSplitWindowIds() { return s_splitWindowIds; }

    // 设置交互模式
    void setInteractionMode(GlobalEnums::InteractionMode mode);
    // 获取当前交互模式
    GlobalEnums::InteractionMode getInteractionMode() const { return m_interactionMode; }

    // 启用/禁用点选功能
    void enablePointSelection(bool enable);
    // 检查点选功能是否启用
    bool isPointSelectionEnabled() const { return m_enablePointSelection; }

    // 高亮显示选中的点
    void highlightPoint(const QPointF &point);

    // 新增功能
    // 添加LxChartData到管理数组
    virtual void AddLxChartData(LxChartData *chartData);
    // 通过曲线唯一ID移除管理数组的曲线
    virtual bool RemoveLxChartDataByUniqueID(QString UniqueID);
    // 删除指定UniqueID的LxChartLegend
    void removeLegendWidget(const QString &UniqueID);
    // 删除全部曲线
    void ClearAllLxChartData();
    // 设置坐标轴范围
    void SetAxisScale();
    // 获取图表截图
    QPixmap getPixmap();

    // 设置光谱亮灭标记Flag
    void setClosestPointMarkable(bool enable) { m_closestPointMarkable = enable; }
    void setClosestLineEnabled(bool enable) { m_closestLineEnabled = enable; }
    void setLegendHoverCurveLight(bool enable) { m_legendHoverCurveLight = enable; }

    // 设置最大LxChartData数量
    void setMaxChartDataCount(int maxCount);
    // 获取当前最大LxChartData数量
    int getMaxChartDataCount() const { return m_maxChartDataCount; }
    // 获取当前LxChartData数量
    int getChartDataCount() const { return m_chartDataVec.size(); }

    // 曲线颜色管理相关
    QColor currentCurveColor;     // 当前悬停的曲线颜色（保留用于兼容性）
    QColor selectCurveColor;      // 当前被选中曲线颜色（点击时变红）
    QColor generateRandomColor(); // 生成随机颜色
    QVector<LxChartData *> m_chartDataVec;

    // MRM数据相关成员变量
    QVector<qreal> m_mrmMzValues; // 存储MRM数据的m/z值，用于X轴标签和双击处理

    // 🎯 新增：MASS位置分配的线程安全保护
    QMutex m_massPositionMutex; // 保护MASS位置分配的互斥锁

    // 🎯 简化的MASS加载计数机制
    mutable QMutex m_massCountMutex;          // 保护MASS计数的互斥锁
    int m_expectedMassCount = 0;              // 预期的MASS数量
    int m_completedMassCount = 0;             // 已完成的MASS数量
    QVector<LxChartData *> m_pendingMassData; // 等待显示的MASS数据
    bool m_isBatchLoading = false;            // 是否正在批量加载

    // 🎯 棒状图计数管理
    mutable QMutex m_barCountMutex; // 保护棒状图计数的互斥锁
    int m_totalBarCount = 0;        // 当前总棒状图数量

    // 获取曲线的原始颜色
    QColor getOriginalColor(const QString &id);
    // 设置曲线为高亮颜色（悬停时变粗）
    void setHighlightWidth(LxChartData *chartData);
    // 设置曲线被选中颜色（点击时变红）
    void setSelectColor(LxChartData *chartData);
    // 恢复曲线的原始颜色和粗细
    void restoreOriginalStyle(LxChartData *chartData);
    // 恢复曲线的原始粗细（悬停离开时）
    void restoreOriginalWidth(LxChartData *chartData);

    // 处理图例点击事件
    virtual void handleLegendClicked(const QString &uniqueId);

    // 同步图例选中状态（虚方法，子类可重写）
    virtual void syncLegendSelection(LxChartData *chartData, bool isSelected);

    // 移动浏览功能（虚方法，子类可重写）
    virtual void browsePrevious();
    virtual void browseNext();

    // 记录上一次高亮的曲线
    LxChartData *lastHighlightedChartData = nullptr;
    LxChartData *lastSelectedChartData = nullptr;

    // 新增函数：设置是否启用背景区域拖动功能
    void setBackgroundAreaDraggable(bool enable) { m_bgAreaDraggable = enable; }
    // 获取背景区域拖动状态
    bool isBackgroundAreaDraggable() const { return m_bgAreaDraggable; }

    // 检查是否靠近自定义区域边界
    bool isNearCustomRangeEdge(const QPointF &pos, int &index, bool &isLeftEdge);

    // 处理自定义区域边缘拖动
    void handleCustomRangeEdgeDragging(const QPointF &pos);

    // 更新自定义区域
    void updateCustomRange(int index);

    // 刷新自定义标注的坐标
    void refreashLabelWidgetPos();

    // 显示峰标记点
    void showPeaks();
    // 隐藏峰标记点
    void hidePeaks();
    // 更新峰标记点位置
    void updatePeaksPos();

    // 连接坐标轴范围变化信号
    void connectAxisRangeChangedSignals();

    // 为MRM数据创建顶点标签
    void createMrmLabels();
    // 更新顶点标签位置
    void updateVertexLabelsPosition();

    // MRM数据显示相关方法
    void addMrmBarLabels(LxChartData *chartData, const QVector<QPointF> &dataPoints);
    void updateMrmLabelsPosition(); // 更新MRM标签位置
    void clearMrmLabels();          // 清除MRM标签

    // 🎯 MRM顶点标签管理（按照峰标签逻辑设计）
    void updateMrmVertexLabels(LxChartData *data, QAbstractSeries *abstractSeries, const QRectF &plotArea, QVector<QRectF> &globalExistingLabels);
    void updateSingleVertexLabel(LxChartData *data, int vertexIndex, const QPointF &vertex, const QString &mzValue,
                                 const QPointF &screenPos, const QRectF &plotArea, QVector<QRectF> &globalExistingLabels, QAbstractSeries *abstractSeries);
    QGraphicsTextItem *findOrCreateVertexLabel(LxChartData *data, int vertexIndex);

    // 🎯 简化的MASS批量加载管理
    void startMassBatch(int expectedCount);      // 开始MASS批量加载
    void onMassCompleted(LxChartData *massData); // MASS加载完成通知
    void removeAllMassData();                    // 移除所有MASS数据
    void updateAllMassSeriesStyle();             // 统一更新所有MASS系列样式

    // 🎯 棒状图计数管理
    void updateBarCount(int delta); // 更新棒状图计数器
    int getTotalBarCount() const;   // 获取当前总棒状图数量
    void setupMrmXAxisLabels(QValueAxis *valueAxisX, const QVector<qreal> &mzValues);
    // 设置峰阴影区域的颜色
    void setPeakShadeColors(const QColor &color1, const QColor &color2);

    // 加载LxChart专用样式
    void loadLxChartStyles();

    // 清除指定曲线的峰标记
    void clearPeaksForChartData(LxChartData *chartData);
    // 清除所有峰标记的图形项（但保留峰数据）
    void clearAllPeakGraphicsItems();

    // 新增：单独控制积分区域显示/隐藏
    void showPeakAreas();
    void hidePeakAreas();
    void togglePeakAreas();
    bool isPeakAreasVisible() const;

    // 阈值控制相关函数
    void setThresholdValue(double threshold);
    void enableThreshold(bool enable);
    double getThresholdValue() const { return m_thresholdValue; }
    bool isThresholdEnabled() const { return m_thresholdEnabled; }

    // 🎯 自定义滑块相关函数
    void initCustomSlider();                                           // 初始化自定义滑块
    void updateCustomSliderPosition();                                 // 更新自定义滑块位置
    void updateDataRange();                                            // 更新数据范围（用于滑块限制）
    void setSliderImageSize(double size) { m_sliderImageSize = size; } // 设置滑块图片大小

private:
    // 峰标记文本位置计算相关函数
    QPointF calculateOptimalLabelPosition(const QPointF &peakPos, const QString &text, const QVector<QRectF> &existingLabels);
    bool isPositionAvailable(const QPointF &pos, const QSizeF &size, const QVector<QRectF> &existingLabels);
    QPointF findAlternativePosition(const QPointF &peakPos, const QSizeF &textSize, const QVector<QRectF> &existingLabels);
    // 创建峰值文本标签
    void createPeakLabel(Peak &peak, LxChartData *chartData, QAbstractSeries *abstractSeries, const QVector<QRectF> &existingLabels, QVector<QRectF> &newLabels);

    // 🎯 顶点标签相关方法
    void createVertexLabel(const QPointF &vertexPos, const QString &labelText, QAbstractSeries *abstractSeries,
                           const QFont &font, const QColor &color, QVector<QRectF> &existingLabels,
                           const QString &filePath, int barIndex);
    QPointF calculateOptimalVertexLabelPosition(const QPointF &vertexScreenPos, const QSizeF &textSize,
                                                const QVector<QRectF> &existingLabels);

    // 阈值控制内部函数
    void updatePeakVisibilityByThreshold();
    void showThresholdLine();
    void hideThresholdLine();
    void updateThresholdLinePosition();

signals:
    // 图表被点击时发出的信号（发送点击位置最近的数据点） QVector<QPair<QString,QPointF>> vecPair:每条Tic曲线对应的最近点和Tic文件路径
    // QPair<QString,QPointF>存储文件路径和最近点，目前每个Tic文件只提取一个Tic曲线，后期提取多条时需要修改此信号及MASS数据提取函数及其他相关函数
    // tuple<QString,QPointF,int> QString路径，QPointF最近点，int事件号
    void sg_showMassChart(QVector<std::tuple<QString, QPointF, int>> vecTuple);

    // 开始计算平均质谱
    void sg_calAvgMass(QVector<std::tuple<QString, int, QVector<double>>> vec);

    // 旧的单个XIC信号已删除，现在使用LxMassChart的sg_showMultipleXicChart处理所有XIC加载
    // void sg_showXicChart(double mz); // 已删除

    // TIC2XIC点击 发送m_defaultSeriesName用于确定唯一FileData对象
    void sg_TIC2XIC_Clicked(QString defaultSeriesName);

    // 移除曲线成功信号 isRemoveFileData:true 所有曲线都被删除了，需要同步删除FileData指针释放资源 fasle:还有曲线没删除，不用释放资源
    void sg_removeData(QString path, bool isRemoveFileData);

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;
    void closeEvent(QCloseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void showMassHightPoint();

    // 子类需要访问的成员变量和方法
    QChart *m_chart;
    CustomChartView *m_chartView;
    QAbstractSeries *defaultSeries = nullptr; // 默认曲线

    // 左键双击显示所有质谱图
    void showMassChart();

    // 检查轨迹类型是否被允许
    bool isTrackTypeAllowed(GlobalEnums::TrackType dataType) const;

    // 更新全局范围（子类需要访问）
    virtual void UpdateGlobalRange();

    // 子类需要访问的标志
    bool m_bool_isRemoveDefaultLegend = false; // 是否移除默认图例

    // 背景区域相关成员变量（子类需要访问）
    QVector<std::tuple<QString, int, QVector<double>>> bgMassPointVec;
    QPair<qreal, qreal> m_backgroundAreaRange; // 存储背景区域的实际X轴范围

private slots:
    // 坐标轴切换
    void on_btn_YAxsixChange_clicked();
    // 设置背景区域
    void on_btn_setBackgroundArea_clicked();
    // 拆分图层
    void on_btn_splitCharts_clicked();

    // TIC->XIC按钮点击
    void on_btn_TIC2XIC_clicked();
    // 显示/隐藏积分区域
    void on_btn_showPeakArea_clicked();

    // 阈值滑块值变化
    void on_verticalSlider_valueChanged(int value);

    // 移动浏览功能
    void on_btn_lastExperiment_clicked();
    void on_btn_nextExperiment_clicked();

    // 导出数据功能
    void handleExportData(const QString &uniqueId);
    void exportChartDataToExcel(LxChartData *chartData, const QString &fileName);

private:
    Ui::LxChart *ui;

    CustomLegendWidget *customLegendWidget = nullptr;

    QString m_windowId;
    QString m_defaultSeriesName;
    bool m_isSplitWindow;
    bool m_isProcessingYAxisChange;
    bool m_isSelectingRegion;
    QGraphicsLineItem *m_startLine;
    QGraphicsLineItem *m_endLine;
    QGraphicsRectItem *m_regionRect;
    qreal m_regionStartX;
    qreal m_regionEndX;
    bool m_isZooming;
    QPointF m_zoomStartPoint;
    QGraphicsRectItem *m_zoomRect;
    QStack<QRectF> m_zoomStack;

    //*背景区域相关 */////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////
    bool m_isSettingBackgroundArea;
    QGraphicsLineItem *m_bgStartLine;
    QGraphicsLineItem *m_bgEndLine;
    QGraphicsRectItem *m_bgRect;

    // 设置背景区域模式
    void setBackgroundAreaMode(bool enabled);
    // 处理背景区域选择
    void handleBackgroundAreaSelection(qreal startX, qreal endX);
    // 清除背景区域
    void clearBackgroundAreaSelection();

    bool m_bgAreaDraggable = true;                 // 背景区域是否可拖动
    bool m_isDraggingBgLeft = false;               // 是否正在拖动背景区域左边界
    bool m_isDraggingBgRight = false;              // 是否正在拖动背景区域右边界
    QPointF m_bgDragStartPoint;                    // 拖动起始点
    qreal m_bgDragStartX = 0;                      // 拖动开始时的X坐标
    int m_bgEdgeDetectionWidth = 5;                // 边缘检测宽度(像素)
    QPair<qreal, qreal> m_tempBackgroundAreaRange; // 临时背景区域范围（拖动过程中）

    // 检查是否在背景区域边缘
    bool isNearBgLeftEdge(const QPointF &pos);
    bool isNearBgRightEdge(const QPointF &pos);
    // 处理背景区域边缘拖动
    void handleBgEdgeDragging(const QPointF &pos);
    // 更新临时背景区域显示
    void updateTempBackgroundArea();

    /**
     * @brief getBgMassPointVec
     * @return  背景质谱数据x点数组：tuple每个Tic数据曲线的x轴数据，用于遍历提取mass
     * tuple中的：QString Tic曲线的路径 intTic曲线事件号 QVector<double> Tic曲线在背景区域范围内的点
     */
    virtual QVector<std::tuple<QString, int, QVector<double>>> getBgMassPointVec();
    //*背景区域相关 */////////////⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️///////////////

    //* 自定义区域相关*/////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////
    QVector<GlobalDefine::CustomRange> vecCustomRange;

    bool m_isCreatingCustomRange;              // 是否正在创建自定义区域
    QGraphicsLineItem *m_customRangeStartLine; // 临时自定义区域起始线
    QGraphicsLineItem *m_customRangeEndLine;   // 临时自定义区域结束线
    QGraphicsRectItem *m_customRangeRect;      // 临时自定义区域矩形
    qreal m_customRangeStartX;                 // 临时自定义区域起始X坐标
    qreal m_customRangeEndX;                   // 临时自定义区域结束X坐标
    QVector<QColor> m_customRangeColors;       // 自定义区域颜色列表

    // 处理自定义区域的函数
    void handleCustomRangeSelection(qreal startX, qreal endX);
    void clearCustomRangeSelection(int index = -1);
    void updateCustomRangeTemp(qreal startX, qreal endX);
    bool isCustomRangeExist(qreal x);
    QVector<int> findCustomRangeAtPosition(const QPointF &pos);

    void updateCustomArea();
    // 自定义区域拖动相关
    bool m_isDraggingCustomRangeLeft = false;  // 是否正在拖动自定义区域左边界
    bool m_isDraggingCustomRangeRight = false; // 是否正在拖动自定义区域右边界
    int m_draggingCustomRangeIndex = -1;       // 当前正在拖动的自定义区域索引
    QPointF m_customRangeDragStartPoint;       // 拖动开始的点位置
    qreal m_customRangeDragStartX = 0.0;       // 拖动开始时的X坐标值

    //* 自定义区域相关*/////////////⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️///////////////

    bool m_isPercentMode;
    qreal m_maxYValue;
    qreal m_minYValue;
    bool m_showCrosshair;
    QGraphicsLineItem *m_crosshairHLine;
    QGraphicsLineItem *m_crosshairVLine;
    QGraphicsTextItem *m_coordinateText;
    int m_crosshairLength;
    Qt::PenStyle m_crosshairStyle;
    bool m_hideCursorWithCrosshair;
    GlobalEnums::InteractionMode m_interactionMode;
    bool m_enablePointSelection;
    QGraphicsEllipseItem *m_highlightPoint;
    QPointF m_currentHighlightPoint;
    QPointF dataPoint;
    bool m_closestLineEnabled;
    bool m_closestPointMarkable;
    bool m_legendHoverCurveLight;
    int m_maxChartDataCount; // 最大LxChartData数量，默认不限制（-1）

    qreal xLeftPoint, xRightPoint, yButtomPoint, yTopPoint; // 整个坐标轴的边界点（用于缩放，不代表最值）

    // 百分比模式下的自定义坐标轴标签
    QList<QGraphicsTextItem *> m_percentageAxisLabels;

    // MRM数据的顶点标签
    QList<QGraphicsTextItem *> m_mrmLabels;

    // 🎯 MRM标签创建标志，避免重复创建
    bool m_mrmLabelsCreated = false;

    // 检查图表类型（隐藏控件）
    void checkChartTypeForHideControls(GlobalEnums::TrackType type);

protected:
    qreal m_globalMinX = 0;
    qreal m_globalMaxX = 1;
    qreal m_globalMinY = 0;
    qreal m_globalMaxY = 1;

    bool m_bool_isDeleteData = false; // 是否正在删除数据中（防止删除过快）
    uint m_uint_legendCount = 0;      // 图例数量 ui->listWidget_legend->count()有时候会有延迟
    // 坐标轴标签默认颜色
    QColor AxisLabelDefaultColor;
    bool isInitAxisLabelDefaultColor = false; // 是否初始化了默认颜色（只要初始化一次）

    // 初始化图表
    void initChart();
    // 处理缩放操作
    void handleZoom(const QRectF &zoomRect);
    // 处理区域选择
    void handleRegionSelection(qreal startX, qreal endX);
    // 清除区域选择
    void clearRegionSelection();
    // 检查鼠标位置是否在X轴区域（plotArea下方）
    bool isInXAxisArea(const QPointF &pos);
    // 检查鼠标位置是否在Y轴区域（plotArea左侧）
    bool isInYAxisArea(const QPointF &pos);
    // 切换Y轴显示模式（百分比/数值）
    void toggleYAxisMode();
    // 拆分图表
    // void splitCharts();

    // 添加到拆分窗口列表
    void addToSplitWindows();
    // 从拆分窗口列表中移除
    void removeFromSplitWindows();

    // 更新十字准星位置
    void updateCrosshair(const QPointF &pos);
    // 显示/隐藏十字准星
    void setCrosshairVisible(bool visible);
    // 清除十字准星 useDefalutCursor是否使用默认光标：true使用 false不使用（）
    void clearCrosshair(bool useDefalutCursor = true);

    // 设置十字准星线型样式
    void setCrosshairStyle(Qt::PenStyle style);

    // 设置十字准星长度
    void setCrosshairLength(int length);

    // 获取当前十字准星样式
    Qt::PenStyle getCrosshairStyle() const { return m_crosshairStyle; }

    // 获取当前十字准星长度
    int getCrosshairLength() const { return m_crosshairLength; }

    // 切换十字准星样式（在虚线和实线之间切换）
    void toggleCrosshairStyle();

    // 获取是否在显示十字准星时隐藏鼠标光标
    bool isHideCursorWithCrosshair() const { return m_hideCursorWithCrosshair; }

    // 更新所有曲线的粗细
    void updateAllSeriesLineWidth();

    // 设置原始坐标轴显示模式状态
    void setOriginAxisModeStyle(bool isPercent);

    // 初始化默认曲线
    void initDefaultSeries();

    /**
     * @brief 显示最近曲线
     * @param event
     */
    void showNearestCurve(QMouseEvent *event);

    /**
     * @brief isPointNearLine
     * @param point
     * @param vecPoint
     * @param threadShold
     * @param PcIndex
     * @return
     */
    bool isPointNearLine(const QPointF &point, const QVector<QPointF> &vecPoint, double threadShold, int PcIndex);

    /**
     * @brief convertToScreenPixel
     * @param point
     * @return
     */
    QPointF convertToScreenPixel(const QPointF &point);

    /**
     * @brief pointToSegmentDistance
     * @param p
     * @param line
     * @return
     */
    double pointToSegmentDistance(const QPointF &p, const QLineF &line);

    // 静态拆分窗口列表
    static QList<QString> s_splitWindowIds;
    // 静态映射表，用于记录哪些曲线已经被拆分

    // 峰标记文本显示相关参数
    int m_peakLabelOffset = 15;          // 峰标记文本的默认偏移距离（像素）
    int m_peakLabelMinDistance = 30;     // 峰标记文本之间的最小距离（像素）
    QFont m_peakLabelFont;               // 峰标记文本字体
    QColor m_peakLabelColor = Qt::black; // 峰标记文本颜色

    // 阈值控制相关成员变量
    double m_thresholdValue = 0.0;                // 当前阈值
    bool m_thresholdEnabled = false;              // 阈值是否启用
    bool m_thresholdManuallySet = false;          // 阈值是否被用户手动设置过
    QGraphicsLineItem *m_thresholdLine = nullptr; // 阈值线

    // 🎯 自定义滑块相关成员变量
    QLabel *m_customSlider = nullptr; // 自定义滑块图片控件
    bool m_isDraggingSlider = false;  // 是否正在拖拽滑块
    QPointF m_sliderDragStartPos;     // 滑块拖拽起始位置
    double m_sliderImageSize = 20.0;  // 滑块图片大小（可调整）
    double m_dataMinY = 0.0;          // 数据的最小Y值（用于滑块范围限制）
    double m_dataMaxY = 1.0;          // 数据的最大Y值（用于滑块范围限制）

    static QMap<QString, bool> s_seriesSplitStatus;

    QList<GlobalEnums::TrackType> allowedTrackTypes; // 允许的轨迹类型列表

    // MRM标签存储
    QList<QGraphicsTextItem *> m_mrmIntensityLabels; // MRM标签（显示 "m/z, 强度" 格式）

    // 颜色管理
    QMap<QString, QString> m_colorMap; // 存储十六进制颜色值，键为LxChartData的UniqueID字符串

    QStack<QString> freeColorStack; // 添加QStack<QString> freeColorStack用于存储被释放的颜色

    /**
     * @brief 检查是否在某个自定义标注上
     * @return
     */
    CustomLabelWidget *isHoverInCustomLabelWidget();
    QVector<CustomLabelWidget *> vecLabelWidget;

    /**
     * @brief 更新标签的数据坐标
     * @param widget 被移动的标签
     */
    void updateLabelPosition(CustomLabelWidget *widget);

    /**
     * @brief 为标注创建对应的标记点
     * @param widget 标注控件
     * @return 创建的标记点对象
     */
    QGraphicsEllipseItem *createMarkerForLabel(CustomLabelWidget *widget);

    // 类成员变量
    bool m_bool_isResize = false;

    // 峰阴影区域的颜色
    QColor m_peakShadeColor1 = QColor(128, 128, 128, 100); // 默认灰色，半透明
    QColor m_peakShadeColor2 = QColor(0, 0, 0, 80);        // 默认黑色，半透明

    // 积分区域显示状态
    bool m_peakAreasVisible = false;

    // 第一次寻峰标志
    bool m_isFirstPeakFinding = true;
};

#endif // LXCHART_H
