# MRM/SIM/FullScan 读取与显示优化 - 重大修改总结

## 📅 修改日期

2025-01-31

## 🎯 核心原则变更

### ❌ 原版本问题

- **推算式数据生成**：根据范围和数据点数量推算 m/z 值
- **不准确的数据显示**：MRM 显示索引而非真实 m/z 值
- **混乱的数据来源**：StreamBody 和 Segment 数据混用

### ✅ 新版本原则

- **禁止推算**：完全禁止任何形式的 m/z 值推算
- **真实数据优先**：读到什么就是什么，确保数据准确性
- **明确数据来源**：严格区分不同数据类型的存储位置

---

## 🔧 重大修改详情

### 1. 离散类型（MRM/SIM）MASS 数据读取位置变更

#### 📍 **数据来源重新定位**

```cpp
// ❌ 原版本：错误地从StreamBody扩展参数读取m/z值
if (streamBodyPtr->lengthParam > 0) {
    // 尝试从扩展参数读取m/z值 - 但MRM数据不在这里！
}

// ✅ 新版本：从Segment中的_EventMRM结构读取真实m/z值
bool getMRMRealMzValues(FileData *data, int eventId, QVector<double> &massX) {
    // 从data->mSegment[0]中解析_EventMRM/_EventMRM2048/_EventSIM
    // 提取真实的mass[]数组数据
}
```

#### 📊 **支持的 MRM 数据类型**

- **\_EventMRM**：标准 MRM 事件
- **\_EventMRM2048**：扩展 MRM 事件（最多 2048 个质量点）
- **\_EventSIM**：SIM 扫描事件

#### 🎯 **m/z 值提取策略**

```cpp
// 严格过滤：只接受合理范围内的正数
if (!std::isnan(mass) && !std::isinf(mass) && mass > 0 && mass < 10000) {
    realMzValues.append(mass);  // 真实m/z值
} else {
    realMzValues.append(i);     // 保持索引作为备选
}
```

### 2. FullScan 数据处理策略调整

#### 📍 **数据来源优先级**

```cpp
// 1. 优先从StreamBody扩展参数读取startMz和stepMz
if (streamBodyPtr->lengthParam >= 2 * sizeof(double)) {
    double startMz = doublePtr[0];
    double stepMz = doublePtr[1];
    // 使用真实参数生成连续序列
}

// 2. 备选：从Segment获取m/z范围
QPair<double, double> mzRange = getMzRangeFromSegment(data, eventId);

// 3. 最后备选：使用索引（避免推算）
for (int i = 0; i < dataPointCount; ++i) {
    massX.append(i);  // 明确标记为索引，不是推算
}
```

### 3. 数据解析方法重构

#### 🔄 **新增解析方法**

```cpp
// 🎯 主要解析入口（禁止推算版本）
bool parseMassData(FileData *data, int eventId, const QByteArray &streamBody,
                   QVector<double> &massX, QVector<double> &massY);

// 🎯 包含Y轴数据的完整解析
bool parseMassDataWithYData(FileData *data, int eventId, const QByteArray &massData,
                            const QByteArray &streamBody, QVector<double> &massX, QVector<double> &massY);

// 🎯 MRM/SIM专用解析
bool parseMRMSIMData(const _StreamBody *streamBodyPtr, const QByteArray &streamBody,
                     QVector<double> &massX, QVector<double> &massY);

// 🎯 FullScan专用解析
bool parseFullScanData(const _StreamBody *streamBodyPtr, const QByteArray &streamBody,
                       QVector<double> &massX, QVector<double> &massY);
```

#### 🔧 **强度数据解析增强**

```cpp
// 🎯 支持所有StreamBody数据类型
template<typename T>
bool parseTypedIntensityData(const char *dataPtr, int dataSize, QVector<double> &massY,
                             int expectedCount, bool isCompressed);

// 支持的数据类型：
// - Type_Uint8/16/32 (压缩/非压缩)
// - Type_Float/Double (压缩/非压缩)
```

### 4. 调用链路重大变更

#### 📞 **旧版本调用链**

```cpp
loadMassDataComplete()
  → parseMassData(massData, streamBody, massX, massY, data, eventId)  // 旧版本
    → 推算m/z值（根据范围和数据点数量）
```

#### 📞 **新版本调用链**

```cpp
loadMassDataComplete()
  → parseMassDataWithYData(data, eventId, massData, streamBody, massX, massY)  // 新版本
    → 根据扫描模式选择策略
      → MRM/SIM: getMRMRealMzValues() → extractMRMMzValues()
      → FullScan: 从扩展参数或Segment读取真实范围
```

---

## ⚠️ 当前已知问题

### 1. XIC 加载失败问题

```
LxDataReader::getMzRangeFromSegment: MRM事件，m/z范围: -5.28733e+307 ~ 4.73278e+290
LxDataReader::loadXicDataBatch: 无效的m/z范围
```

**原因分析**：

- MRM2048 数组中包含无效数据（极大值、负数）
- 范围计算时包含了这些异常值

**修复状态**：

- ✅ 已添加严格的 m/z 值过滤：`mass > 0 && mass < 10000`
- ✅ 限制 MRM2048 处理数量：只处理前 100 个有效数据
- ⏳ 需要进一步测试验证

### 2. Qt Charts 崩溃问题

```
尝试移除ID为 "xxx" 的控件 2
成功移除ID为 "xxx" 的控件
尝试移除ID为 "xxx" 的控件 1
未找到ID为 "xxx" 的控件
程序崩溃
```

**原因分析**：

- MRM 系列的重复创建和删除
- 多线程环境下的对象访问冲突
- 标签的重复清理

**修复状态**：

- ✅ 已添加数据验证，确保 X/Y 轴数据一致性
- ✅ 已添加线程信息调试
- ⏳ 需要进一步优化对象生命周期管理

---

## 🎯 关键技术要点

### 1. 数据验证增强

```cpp
// 🎯 确保X和Y数据一致性
if (massX.size() != massY.size()) {
    int minSize = qMin(massX.size(), massY.size());
    massX.resize(minSize);
    massY.resize(minSize);
}
```

### 2. 严格的 m/z 过滤

```cpp
// 🎯 只接受合理范围内的质量值
if (!std::isnan(mass) && !std::isinf(mass) && mass > 0 && mass < 10000) {
    // 有效的m/z值
}
```

### 3. 扫描模式自动检测

```cpp
GlobalEnums::ScanMode currentScanMode = getCurrentScanMode(*data);
if (currentScanMode == GlobalEnums::ScanMode::MRM || currentScanMode == GlobalEnums::ScanMode::SIM) {
    // MRM/SIM处理逻辑
} else {
    // FullScan处理逻辑
}
```

---

## 📋 测试验证要点

### 1. MRM 数据验证

- ✅ **m/z 值显示**：应显示真实值（如 678.5, 425.1, 443.2）而非索引（0,1,2）
- ⏳ **XIC 加载**：双击 MRM 棒子应成功加载 XIC
- ⏳ **标签显示**：MRM 棒子顶部应显示正确的 m/z 标签

### 2. FullScan 数据验证

- ⏳ **X 轴刻度**：应显示正常的 m/z 刻度，不被隐藏
- ⏳ **范围显示**：应显示合理的 m/z 范围（如 50-1000）
- ⏳ **连续性**：X 轴应为连续的 m/z 序列

### 3. 系统稳定性验证

- ⏳ **无崩溃**：重复加载/删除 MASS 数据不应崩溃
- ⏳ **内存管理**：Qt Charts 对象应正确创建和销毁
- ⏳ **多线程安全**：数据加载和 UI 操作应线程安全

---

## 🔄 后续优化方向

### 1. 性能优化

- 减少 MRM 系列的重复创建
- 优化标签的批量更新
- 改进多线程数据加载策略

### 2. 数据准确性

- 完善 XIC 的 m/z 范围计算
- 优化 FullScan 的真实范围获取
- 增强数据验证和错误处理

### 3. 用户体验

- 统一 MRM 和 FullScan 的显示风格
- 改进加载进度提示
- 优化图表交互响应速度

---

## 📝 重要提醒

1. **禁止推算原则**：任何情况下都不应推算 m/z 值，必须从实际数据读取
2. **数据来源明确**：MRM 从 Segment 读取，FullScan 优先从 StreamBody 读取
3. **线程安全**：所有 Qt Charts 操作必须在主线程进行
4. **对象生命周期**：确保 MRM 系列和标签的正确创建和销毁顺序

---

## 🏗️ 架构变更详情

### 5. 顶点标签创建与销毁机制重构

#### 📍 **MRM 标签生命周期管理**

```cpp
// 🎯 标签创建时机
LxChart::onMassCompleted() {
    // 1. 所有MASS数据加载完成后
    // 2. 批量创建MRM顶点标签
    // 3. 避免重复创建和删除
}

// 🎯 标签清理策略
LxChart::clearMrmLabels() {
    // 1. 删除数据前先清理标签
    // 2. 避免访问已删除的系列对象
    // 3. 防止重复清理
}
```

#### ⚠️ **标签相关崩溃风险**

- **重复清理**：同一批标签被多次清理
- **时序问题**：标签清理与系列删除的时序冲突
- **引用失效**：标签引用已删除的系列对象

### 6. 离散类型 MASS 存储与析构方式变更

#### 📊 **MRM 数据存储结构**

```cpp
// 🎯 MRM垂直线条存储
LxChartData::createSeries() {
    // 1. 创建QBarSeries而非QLineSeries
    // 2. 每个m/z值对应一个垂直线条
    // 3. 使用类别轴显示m/z值
    categories: ("678.5", "425.1", "443.2", "424.1", "263.5", "163.0")
}

// 🎯 系列管理
QVector<QAbstractBarSeries*> m_mrmBarSeries;  // MRM棒子系列数组
QAbstractSeries* m_mainSeries;                // 主系列引用
```

#### 🔄 **析构顺序优化**

```cpp
// 🎯 安全的析构顺序
LxChartData::clearAllSeries() {
    // 1. 先清理MRM棒子系列
    clearMrmBarSeries();

    // 2. 再清理主系列引用
    if (m_mainSeries) {
        deleteSeries(m_mainSeries);
        m_mainSeries = nullptr;
    }

    // 3. 最后清理图表引用
}
```

### 7. X 轴刻度显示策略调整

#### 📍 **MRM 模式 X 轴处理**

```cpp
// 🎯 MRM数据隐藏X轴刻度
LxChart::setupMrmXAxisLabels() {
    // 1. 隐藏数值刻度（0,1,2,3...）
    // 2. 显示m/z值作为类别标签
    // 3. 防止FullScan数据被误处理
}
```

#### 📍 **FullScan 模式 X 轴处理**

```cpp
// 🎯 FullScan数据保持正常刻度
// 1. 不调用setupMrmXAxisLabels()
// 2. 使用连续的m/z值作为X轴
// 3. 显示正常的数值刻度
```

---

## 🔍 调试与诊断工具

### 1. 详细的日志输出

```cpp
// 🎯 数据来源追踪
qDebug() << "LxDataReader::parseMassDataWithYData: 解析质谱数据（包含Y轴数据）";
qDebug() << "   massData大小:" << massData.size();
qDebug() << "   streamBody大小:" << streamBody.size();

// 🎯 线程信息追踪
qDebug() << "   当前线程:" << QThread::currentThread();
qDebug() << "   主线程:" << QCoreApplication::instance()->thread();

// 🎯 m/z值验证
qDebug() << "     有效m/z[" << i << "] =" << mass;
qDebug() << "     无效m/z[" << i << "] = 无效值:" << mass;
```

### 2. 数据完整性检查

```cpp
// 🎯 X/Y轴数据一致性验证
if (massX.size() != massY.size()) {
    qDebug() << "   ❌ 警告：X轴和Y轴数据数量不匹配！";
    qDebug() << "     X轴数量:" << massX.size() << ", Y轴数量:" << massY.size();
}

// 🎯 m/z范围合理性检查
qDebug() << "   范围有效性检查:" << (validCount > 0) << "&&" << (maxValidMass > minValidMass);
```

### 3. 系列管理状态追踪

```cpp
// 🎯 系列创建追踪
qDebug() << "LxChartData::createSeries: 开始创建系列，UniqueID:" << m_uniqueID;
qDebug() << "LxChartData::createSeries: 创建MRM垂直线条，数据点数:" << dataCount;

// 🎯 系列删除追踪
qDebug() << "LxChartData::clearMrmBarSeries: 开始清除MRM棒子系列，数量:" << m_mrmBarSeries.size();
qDebug() << "LxChart::RemoveLxChartDataByUniqueID: 默认系列已被删除，清空引用";
```

---

## 📊 性能影响分析

### 1. 内存使用变化

- **增加**：MRM 数据需要额外存储真实 m/z 值
- **减少**：避免了重复的推算计算
- **优化**：限制 MRM2048 处理数量，减少无效数据处理

### 2. CPU 使用变化

- **增加**：Segment 解析需要额外的 CPU 时间
- **减少**：避免了实时的 m/z 推算计算
- **优化**：缓存解析结果，避免重复解析

### 3. 响应速度影响

- **MASS 加载**：略有增加（增加了 Segment 解析）
- **XIC 加载**：应该改善（更准确的范围计算）
- **图表渲染**：可能略有增加（MRM 垂直线条）

---

## 🎯 兼容性考虑

### 1. 数据文件兼容性

- ✅ **向后兼容**：支持原有的.Param 和.dat 文件格式
- ✅ **多版本支持**：同时支持 MRM 和 MRM2048 格式
- ⚠️ **数据验证**：对异常数据有更严格的过滤

### 2. API 兼容性

- ✅ **保留旧接口**：原有的 parseMassData 方法仍然存在
- ✅ **新增接口**：新的解析方法作为补充
- ⚠️ **调用变更**：loadMassDataComplete 中的调用已切换到新方法

### 3. 显示兼容性

- ⚠️ **MRM 显示变更**：从索引显示改为真实 m/z 值显示
- ⚠️ **X 轴处理变更**：MRM 和 FullScan 的 X 轴处理策略不同
- ✅ **用户体验改善**：数据显示更加准确

---

## 🚨 紧急修复指南

### 如果遇到崩溃问题：

#### 1. 临时禁用 MRM 标签

```cpp
// 在LxChart::onMassCompleted()中注释掉：
// clearMrmLabels();
// findOrCreateVertexLabel(...);
```

#### 2. 简化 MRM 数据处理

```cpp
// 在extractMRM2048MzValues()中使用索引：
for (int i = 0; i < extractCount; ++i) {
    realMzValues.append(i);  // 临时使用索引
}
```

#### 3. 回退到旧版本解析

```cpp
// 在loadMassDataComplete()中切换回旧方法：
// parseMassData(massData, streamBody, massX, massY, data, eventId);  // 旧版本
```

### 如果遇到 XIC 加载失败：

#### 1. 检查 m/z 范围计算

```cpp
// 查看getMzRangeFromSegment的输出日志
// 确认范围是否合理（如：163 ~ 678.5）
```

#### 2. 临时使用固定范围

```cpp
// 在getMzRangeFromSegment()中返回固定范围：
return qMakePair(100.0, 1000.0);  // 临时固定范围
```

---

_本文档详细记录了 2025-07-31 的重大架构调整，包含所有关键技术细节和应急处理方案。_
