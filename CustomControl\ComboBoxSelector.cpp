#include "ComboBoxSelector.h"
#include <QDebug>

ComboBoxSelector::ComboBoxSelector(QWidget *parent) : QComboBox(parent)
{
    // 连接activated信号到自定义的槽函数
    connect(this, QOverload<int>::of(&QComboBox::activated), this, &ComboBoxSelector::handleItemActivated);
}

ComboBoxSelector::~ComboBoxSelector()
{
}

void ComboBoxSelector::showPopup()
{
    // 调用基类的showPopup来显示下拉列表
    QComboBox::showPopup();
}

void ComboBoxSelector::handleItemActivated(int index)
{
    // 发出自定义信号，通知用户点击了某个下拉项
    emit itemClicked(index);
    qDebug() << "选择了" << index;
    // 始终保持选择第一个项
    if (count() > 0) {
        setCurrentIndex(0);
    }

    // 阻止QComboBox更新当前项
    blockSignals(true);
    setCurrentIndex(0);
    blockSignals(false);
}
