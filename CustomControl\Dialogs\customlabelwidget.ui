<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CustomLabelWidget</class>
 <widget class="QWidget" name="CustomLabelWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>150</width>
    <height>50</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>50</width>
    <height>50</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>150</width>
    <height>50</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QWidget" name="widget" native="true">
     <property name="styleSheet">
      <string notr="true">#widget{
border: 2px solid gray; /* 设置边框为2像素的灰色实线 */
 
border-radius: 0px; /* 设置圆角弧度为5像素 */
}</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,0">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="label">
        <property name="cursor">
         <cursorShape>SizeAllCursor</cursorShape>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QPushButton" name="btn_hide">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>24</width>
            <height>24</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>24</width>
            <height>24</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="icon">
           <iconset resource="../../res.qrc">
            <normaloff>:/Icons/QAction/hide.png</normaloff>:/Icons/QAction/hide.png</iconset>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btn_delete">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>24</width>
            <height>24</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>24</width>
            <height>24</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="icon">
           <iconset resource="../../res.qrc">
            <normaloff>:/Icons/QAction/deleteLabel.png</normaloff>:/Icons/QAction/deleteLabel.png</iconset>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../res.qrc"/>
 </resources>
 <connections/>
</ui>
