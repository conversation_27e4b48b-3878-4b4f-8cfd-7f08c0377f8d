#include "filedata.h"
#include "LxChart/ticchartdata.h"
// #include "mrmreader.h"  // 已迁移到LxDataReader
#include <QDebug>
#include <QThread>
#include <QCoreApplication>
#include <QMetaObject>

FileData::FileData(QString ParamPath, QObject *parent) : QObject{parent}, m_qstr_filePath(ParamPath)
{
    // qDebug() << "FileData::FileData: 创建FileData，路径:" << ParamPath << "，对象地址:" << this;
    // 移除xicChartData的创建，现在XIC数据由TicChartData管理
}

FileData::~FileData()
{
    qDebug() << "FileData::~FileData: 开始析构，路径:" << m_qstr_filePath << "，当前线程:" << QThread::currentThread();

    // 清理TicMap中的TicChartData对象（会自动清理内部的MASS和XIC）
    qDebug() << "FileData::~FileData: 清理TicMap，数量:" << TicMap.size();
    for (auto it = TicMap.begin(); it != TicMap.end(); ++it)
    {
        if (it.value())
        {
            qDebug() << "FileData::~FileData: 删除TicChartData，事件ID:" << it.key();
            delete it.value(); // TicChartData析构会自动清理MASS和XIC
        }
    }
    TicMap.clear();

    qDebug() << "FileData::~FileData: 析构完成，所有数据已清理";
}

QString FileData::getFilePath() const
{
    return m_qstr_filePath;
}

// 新的TIC数据管理方法实现
TicChartData *FileData::getTicData(int eventId) const
{
    return TicMap.value(eventId, nullptr);
}

TicChartData *FileData::createTicData(int eventId)
{
    if (TicMap.contains(eventId))
    {
        qDebug() << "FileData::createTicData: TIC数据已存在，事件ID:" << eventId;
        return TicMap[eventId];
    }

    // qDebug() << "FileData::createTicData: 准备创建TIC数据，事件ID:" << eventId << "，当前线程:" << QThread::currentThread();

    TicChartData *ticData = nullptr;

    // 动态获取指定Event的扫描模式
    // 需要通过FileDataManager获取LxDataReader来检测扫描模式
    GlobalEnums::ScanMode scanMode = GlobalEnums::ScanMode::FullScan; // 默认值

    // 尝试获取正确的扫描模式（如果Segment数据已加载）
    if (!mSegment.empty())
    {
        // 这里需要通过某种方式获取LxDataReader实例来检测扫描模式
        // 由于FileData不直接持有LxDataReader引用，暂时使用默认值
        // 实际的扫描模式检测应该在数据加载完成后进行
        qDebug() << "FileData::createTicData: Segment数据已存在，但暂时使用默认扫描模式";
    }

    // 检查是否在主线程中
    if (QThread::currentThread() == QCoreApplication::instance()->thread())
    {
        // 在主线程中，直接创建
        ticData = new TicChartData(m_qstr_filePath, eventId, GlobalEnums::IonMode::NagativeIon,
                                   scanMode, "无处理", "默认数据名", "默认样本名", this);
        qDebug() << "FileData::createTicData: 在主线程中创建TIC数据，扫描模式:" << (int)scanMode;
    }
    else
    {
        // 不在主线程中，使用QMetaObject::invokeMethod在主线程中创建
        // qDebug() << "FileData::createTicData: 不在主线程中，使用invokeMethod创建";
        QMetaObject::invokeMethod(this, [this, eventId, scanMode, &ticData]()
                                  {
                                      ticData = new TicChartData(m_qstr_filePath, eventId, GlobalEnums::IonMode::NagativeIon,
                                                                 scanMode, "无处理", "默认数据名", "默认样本名", this);
                                      qDebug() << "FileData::createTicData: 在主线程中通过invokeMethod创建TIC数据，扫描模式:" << (int)scanMode; }, Qt::BlockingQueuedConnection);
    }

    if (ticData)
    {
        TicMap[eventId] = ticData;
        // qDebug() << "FileData::createTicData: 创建TIC数据成功，事件ID:" << eventId;
    }
    else
    {
        qDebug() << "FileData::createTicData: 创建TIC数据失败，事件ID:" << eventId;
    }

    return ticData;
}

void FileData::removeTicData(int eventId)
{
    if (TicMap.contains(eventId))
    {
        TicChartData *ticData = TicMap[eventId];
        if (ticData)
        {
            qDebug() << "FileData::removeTicData: 删除TIC数据，事件ID:" << eventId;
            delete ticData;
        }
        else
        {
            qDebug() << "FileData::removeTicData: TIC数据指针为空，事件ID:" << eventId;
        }
        TicMap.remove(eventId);
    }
    else
    {
        qDebug() << "FileData::removeTicData: 事件ID不存在:" << eventId;
    }
}

QList<int> FileData::getAllEventIds() const
{
    return TicMap.keys();
}
