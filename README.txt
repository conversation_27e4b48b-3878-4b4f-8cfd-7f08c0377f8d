# 三重四极杆质谱仪数据读取器

## 项目概述

本项目提供了一个针对三重四极杆质谱仪数据的读取和分析工具。重点功能包括：

1. 读取.Param格式的参数文件
2. 读取.Dat格式的数据文件
3. 显示TIC(总离子流)图表
4. 显示质谱图

## 新增的ParamFileReader类

ParamFileReader类是专门设计用于读取和解析.Param格式文件的工具类。该类可以：

- 读取.Param文件的二进制内容
- 解析文件头信息
- 提取和解析配置节数据(如Q1Scan、Q3Scan等节)
- 解析TIC数据(时间点和强度)
- 获取对应的.Dat文件路径

## 测试程序

项目包含一个测试模式，可通过命令行参数激活：

```
HZHDataReader.exe --test
```

可以指定要测试的.Param文件路径：

```
HZHDataReader.exe --test --file="D:/path/to/your/file.Param"
```

## 文件格式说明

### .Param文件格式

.Param文件是二进制格式，包含以下主要部分：

1. 文件头部分(前100字节左右)
2. 配置节部分(包含[Q1Scan]、[Q3Scan]等配置节)
3. TIC数据部分(包含时间点和强度值)

### .Dat文件格式

.Dat文件包含具体的质谱数据，每个时间点对应的质荷比和强度值。

## 开发人员使用说明

ParamFileReader类的主要用法：

```cpp
// 创建ParamFileReader对象
ParamFileReader reader;

// 读取.Param文件
bool success = reader.readFile("path/to/file.Param");

if (success) {
    // 获取TIC数据
    QVector<double> timePoints = reader.getTicTimePoints();
    QVector<double> intensities = reader.getTicIntensities();
    
    // 获取配置信息
    QMap<QString, QMap<QString, QString>> configs = reader.getScanConfigs();
    
    // 获取对应的.Dat文件路径
    QString datFile = reader.getDatFilePath();
    
    // 打印调试信息
    reader.debugPrintAll();
}
```

## 注意事项

- 确保.Param文件和对应的.Dat文件位于同一目录
- 文件格式的解析基于对文件结构的分析，可能需要针对不同仪器生成的文件进行调整 




TIC数据点索引 i → mFrame[indexFile] = i → 实际帧索引 = mIndexArray[indexFile][i] → 质谱数据