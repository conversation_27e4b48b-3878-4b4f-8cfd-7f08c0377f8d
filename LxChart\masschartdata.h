#ifndef MASSCHARTDATA_H
#define MASSCHARTDATA_H

#include "lxchartdata.h"
#include <QVector>
#include <QPointF>

/**
 * @brief MassChartData类用于给LxChart控件提供Mass数据
 * 集成了原MassData结构体的所有数据成员，移除了frameIndex缓存机制
 */
class MassChartData : public LxChartData
{
    Q_OBJECT
public:
    /**
     * @brief 构造函数
     * @param ParamPath 从属的FileData对象Param文件绝对路径
     * @param ionMode 离子模式
     * @param scanMode 扫描模式
     * @param dataProcess 数据处理方式
     * @param dataName 数据名
     * @param sampleName 样本名
     * @param eventNum 事件号
     * @param parent 父对象
     */
    explicit MassChartData(QString ParamPath, GlobalEnums::IonMode ionMode = GlobalEnums::IonMode::NagativeIon,
                           GlobalEnums::ScanMode scanMode = GlobalEnums::ScanMode::MRM, QString dataProcess = "无处理", QString dataName = "默认数据名",
                           QString sampleName = "默认样本名", int eventNum = 0, QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~MassChartData();

    // 使用基类的数据，不再重复存储
    QVector<QPointF> getMassData() const { return getData(); }
    void setMassData(const QVector<QPointF> &data) { setData(data); }

    // 使用基类的范围方法
    double getMaxX() const { return LxChartData::getMaxX(); }
    double getMinX() const { return LxChartData::getMinX(); }
    double getMaxY() const { return LxChartData::getMaxY(); }
    double getMinY() const { return LxChartData::getMinY(); }

    void setRange(double minX, double maxX, double minY, double maxY)
    {
        m_minX = minX;
        m_maxX = maxX;
        m_minY = minY;
        m_maxY = maxY;
        setYAxisRange(minY, maxY);
        setXAxisRange(minX, maxX);
    }

    QString getMassTitle() const { return getTitle(); }
    void setMassTitle(const QString &title) { setTitle(title); }

    // 一次性设置所有MASS数据
    void setMassDataComplete(const QVector<QPointF> &data, double minX, double maxX,
                             double minY, double maxY, const QString &title)
    {
        // 只设置基类数据，不再重复存储
        setDataWithRange(data, minX, maxX, minY, maxY);
        setTitle(title);

        // 设置范围到成员变量（用于快速访问）
        m_minX = minX;
        m_maxX = maxX;
        m_minY = minY;
        m_maxY = maxY;
    }

    int getTic_event_id() const;
    void setTic_event_id(int newTic_event_id);

    // MRM柱子宽度设置（便捷方法）
    void setMrmBarWidth(qreal width) { setBarWidth(width); }
    qreal getMrmBarWidth() const { return getBarWidth(); }

private:
    int tic_event_id;

    // 只保留范围信息用于快速访问（数据存储在基类中）
    double m_maxX = 0.0, m_minX = 0.0, m_maxY = 0.0, m_minY = 0.0;
};

#endif // MASSCHARTDATA_H
