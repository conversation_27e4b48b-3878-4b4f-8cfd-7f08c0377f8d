#include "taskmanager.h"

// 初始化静态成员变量
TaskManager *TaskManager::m_instance = nullptr;
QMutex TaskManager::m_mutex;

// 获取全局唯一实例
TaskManager *TaskManager::instance()
{
    if (!m_instance) {
        QMutexLocker locker(&m_mutex);
        if (!m_instance) {
            m_instance = new TaskManager(nullptr);
            // qDebug() << "创建TaskManager单例实例";
        }
    }
    return m_instance;
}

TaskManager::TaskManager(QObject *parent) : QObject(parent)
{
    // 默认使用系统可用处理器数量的一半作为线程数，确保至少有2个线程
    int optimalThreadCount = qMax(2, QThread::idealThreadCount() / 2);

    // 全局线程池默认值可能是1，确保设置正确
    QThreadPool::globalInstance()->setMaxThreadCount(optimalThreadCount);

    // 设置实例线程池
    m_threadPool.setMaxThreadCount(optimalThreadCount);
    m_threadPool.setExpiryTimeout(30000); // 线程空闲30秒后销毁

    // qDebug() << "TaskManager初始化完成";
    // qDebug() << " - 实例线程池大小：" << m_threadPool.maxThreadCount();
    // qDebug() << " - 全局线程池大小：" << QThreadPool::globalInstance()->maxThreadCount();
    // qDebug() << " - 当前线程：" << QThread::currentThread();
    // qDebug() << " - 处理器核心数：" << QThread::idealThreadCount();
}

TaskManager::~TaskManager()
{
    // 等待所有任务完成后退出
    m_threadPool.waitForDone();
    qDebug() << "TaskManager析构，所有任务已完成";

    // 清除单例指针
    m_instance = nullptr;
}

void TaskManager::setMaxThreadCount(int count)
{
    // 确保不设置为0或负数
    count = qMax(1, count);

    m_threadPool.setMaxThreadCount(count);
    QThreadPool::globalInstance()->setMaxThreadCount(count);

    // qDebug() << "线程池大小已设置为：" << count;
    // qDebug() << " - 实例线程池大小：" << m_threadPool.maxThreadCount();
    // qDebug() << " - 全局线程池大小：" << QThreadPool::globalInstance()->maxThreadCount();
}

int TaskManager::maxThreadCount() const
{
    return m_threadPool.maxThreadCount();
}

int TaskManager::activeThreadCount() const
{
    return m_threadPool.activeThreadCount();
}
