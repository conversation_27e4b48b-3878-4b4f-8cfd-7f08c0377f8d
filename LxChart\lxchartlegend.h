#ifndef LXCHARTLEGEND_H
#define LXCHARTLEGEND_H

#include <QWidget>
#include <QEvent>
#include <QDebug>
#include <QMouseEvent>
#include <QMenu>
#include <QColorDialog>
#include "CustomControl/Dialogs/linewidthdialog.h"
#include <QFontDialog>
#include <QIcon>
#include <QUuid>
#include "Globals/GlobalEnums.h"

namespace Ui
{
    class LxChartLegend;
}

class LxChartLegend : public QWidget
{
    Q_OBJECT

public:
    explicit LxChartLegend(QString fileDataPath, QString title, QString color, QString UniqueID, QString toolTip,
                           GlobalEnums::TrackType trackType = GlobalEnums::TrackType::TIC, QWidget *parent = nullptr);
    ~LxChartLegend();

    QString getColor() const;
    void setColor(const QString &newColor);

    QString getToolTip() const;

    bool bool_curveVisible() const;

    QString getUniqueID() const;

    QString path() const;

    QString title() const;
    void setTitle(const QString &newTitle);

    // 类型管理
    GlobalEnums::TrackType getTrackType() const;
    void setTrackType(GlobalEnums::TrackType type);

    // 选中状态管理
    bool isChecked() const;
    void setChecked(bool checked);
    void updateSelectedStyle();

private:
    void initMenu();
    void connectAll();
    void updateTypeDisplay();
    void loadLxChartStyles(); // 加载LxChart样式

    // 创建右键菜单
    QMenu *contextMenu = nullptr;
    QAction *deleteAction = nullptr;
    QAction *hideAction = nullptr;
    QAction *colorAction = nullptr;
    QAction *widthAction = nullptr;
    QAction *fontAction = nullptr;
    QAction *exportDataAction = nullptr;

private:
    QString m_path;
    QString m_title; // 标题（和LxChartData中的标题一样）
    QString m_color;
    QString m_toolTip;
    QString UniqueID; // 和LxChartData一样的唯一ID
    bool m_bool_curveVisible = true;
    int m_int_width = 1;
    bool m_checked = false; // 选中状态

    // 曲线类型
    GlobalEnums::TrackType m_trackType;

private slots:
    void exportData(); // 导出数据槽函数

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;
signals:
    void sg_changeVisible(QString id, bool visible);
    void sg_deleteLegend(QString id);
    void sg_changeColor(QString id, QColor color);
    void sg_changeLineWidth(QString id, int width);
    void sg_legendClicked(QString id); // 新增：图例被点击信号
    void sg_exportData(QString id);    // 新增：导出数据信号

private:
    Ui::LxChartLegend *ui;
};

#endif // LXCHARTLEGEND_H
