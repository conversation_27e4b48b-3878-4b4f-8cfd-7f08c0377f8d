#include "customlabelinputdialog.h"
#include "ui_customlabelinputdialog.h"

CustomLabelInputDialog::CustomLabelInputDialog(QWidget *parent) : QDialog(parent), ui(new Ui::CustomLabelInputDialog)
{
    ui->setupUi(this);
    setWindowTitle(tr("编辑标签内容"));
    textFont = ui->textEdit->font();

    connectAll();
}

CustomLabelInputDialog::~CustomLabelInputDialog()
{
    delete ui;
}

void CustomLabelInputDialog::connectAll()
{
    connect(ui->btn_cancel, &QPushButton::clicked, this, &CustomLabelInputDialog::close);
    connect(ui->btn_ok, &QPushButton::clicked, this, [=]() {
        content = ui->textEdit->toPlainText();
        emit contentReady();
    });

    connect(ui->btn_font, &QPushButton::clicked, [=]() {
        QFont selectedFont = QFontDialog::getFont(nullptr, QFont(), this);
        // 检查用户是否选择了字体
        if (selectedFont != QFont()) {
            // 设置字体
            ui->textEdit->setFont(selectedFont);
        }

        textFont = ui->textEdit->font();
    });

    connect(ui->btn_color, &QPushButton::clicked, [=]() {
        QColor color = QColorDialog::getColor(Qt::white, this, tr("请选择颜色"));
        if (color == QColor()) {
            qDebug() << "颜色为空";
            return;
        }

        ui->textEdit->setTextColor(color);
        // 修改已存在文本的颜色
        QTextCursor cursor = ui->textEdit->textCursor();
        cursor.select(QTextCursor::Document); // 选择整个文档

        // 设置新的前景色
        QTextCharFormat format;
        format.setForeground(color);
        cursor.mergeCharFormat(format); // 应用新的格式到选中的文本

        textColor = color;
    });
}

std::tuple<QString, QFont, QColor> CustomLabelInputDialog::getContent()
{
    CustomLabelInputDialog instance;
    instance.show(); // 显示界面

    // 事件循环，等待用户操作
    QEventLoop loop;
    connect(&instance, &CustomLabelInputDialog::contentReady, [&loop]() {
        loop.quit(); // 退出事件循环
    });

    loop.exec(); // 进入事件循环
    std::tuple<QString, QFont, QColor> tuple = std::make_tuple(instance.content, instance.textFont, instance.textColor);
    return tuple; // 返回获取的内容
}
