#ifndef PARAMFILEREADER_H
#define PARAMFILEREADER_H

#include <QObject>
#include <QFile>
#include <QByteArray>
#include <QMap>
#include <QVector>
#include <QDebug>
#include <cmath>

/**
 * @brief 用于读取.Param格式文件的专用类
 * 
 * 该类提供了读取和解析三重四极杆质谱仪.Param文件的功能
 */
class ParamFileReader : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父QObject
     */
    explicit ParamFileReader(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~ParamFileReader();
    
    /**
     * @brief 读取.Param文件
     * @param filePath 文件路径
     * @return 是否成功读取
     */
    bool readFile(const QString &filePath);
    
    /**
     * @brief 获取文件头数据
     * @return 文件头二进制数据
     */
    QByteArray getFileHeader() const;
    
    /**
     * @brief 获取扫描配置节数据
     * @return 扫描配置映射表
     */
    QMap<QString, QMap<QString, QString>> getScanConfigs() const;
    
    /**
     * @brief 获取帧索引数组
     * @return 帧索引数组
     */
    QVector<qint64> getFrameIndices() const;
    
    /**
     * @brief 获取TIC时间点数据
     * @return TIC时间点数组
     */
    QVector<double> getTicTimePoints() const;
    
    /**
     * @brief 获取TIC强度数据
     * @return TIC强度数组
     */
    QVector<double> getTicIntensities() const;
    
    /**
     * @brief 打印所有读取到的数据
     * 将所有读取到的数据以调试信息输出
     */
    void debugPrintAll() const;
    
    /**
     * @brief 获取对应的.Dat文件路径
     * @return .Dat文件路径
     */
    QString getDatFilePath() const;

private:
    /**
     * @brief 解析文件头数据
     * @param data 文件二进制数据
     * @return 是否成功解析
     */
    bool parseHeader(const QByteArray &data);
    
    /**
     * @brief 解析扫描配置节数据
     * @param data 文件二进制数据
     * @return 是否成功解析
     */
    bool parseScanConfigs(const QByteArray &data);
    
    /**
     * @brief 解析TIC数据
     * @param data 文件二进制数据
     * @return 是否成功解析
     */
    bool parseTicData(const QByteArray &data);
    
    // 成员变量
    QString m_filePath;              // 文件路径
    QByteArray m_fileHeader;         // 文件头数据
    QMap<QString, QMap<QString, QString>> m_scanConfigs;  // 扫描配置
    QVector<qint64> m_frameIndices;  // 帧索引
    QVector<double> m_ticTimePoints; // TIC时间点
    QVector<double> m_ticIntensities; // TIC强度
    
    // 记录文件结构信息
    int m_headerSize = 0;            // 文件头大小
    int m_configSectionOffset = 0;   // 配置节偏移量
    int m_configSectionSize = 0;     // 配置节大小
    int m_dataOffset = 0;            // 数据区偏移量
    int m_dataSize = 0;              // 数据区大小
};

#endif // PARAMFILEREADER_H 