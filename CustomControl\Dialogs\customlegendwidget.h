#ifndef CUSTOMLEGENDWIDGET_H
#define CUSTOMLEGENDWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QScrollArea>
#include "LxChart/lxchartlegend.h"
namespace Ui
{
    class CustomLegendWidget;
}

class CustomLegendWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CustomLegendWidget(QWidget *parent = nullptr);
    ~CustomLegendWidget();
    bool removeLegendWidget(QString id);
    void addLegendWidget(LxChartLegend *legend);
    void connectAll();
    void resetExpandStatus();
signals:
    // isExpand 是否放大 true 放大 false缩小
    // void sg_expand(bool isExpand);

private:
    Ui::CustomLegendWidget *ui;
    uint m_uint_legendCount = 0;
    bool m_bool_expand = false;

    // ScrollArea相关成员
    QWidget *m_contentWidget = nullptr;
    QVBoxLayout *m_layout = nullptr;
};

#endif // CUSTOMLEGENDWIDGET_H
