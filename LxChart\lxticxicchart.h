#ifndef LXTICXICCHART_H
#define LXTICXICCHART_H

#include <QObject>
#include <QMap>
#include "Globals/GlobalEnums.h"
#include "LxChart/lxchart.h"
#include "LxChart/ticchartdata.h"
#include "LxChart/xicchartdata.h"

/**
 * @brief LxTicXicChart类用于同时显示TIC和XIC曲线
 *
 * 功能特点：
 * 1. TIC和XIC曲线一对一关联，颜色保持一致
 * 2. 双击时按照原有逻辑显示MASS数据（仅对TIC曲线有效）
 * 3. XIC曲线仅支持基础交互（悬停变红、单击变粗），不参与计算
 * 4. 通过tic_event_id字段关联TIC和XIC数据
 */
class LxTicXicChart : public LxChart
{
    Q_OBJECT
public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit LxTicXicChart(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~LxTicXicChart();

    /**
     * @brief 添加TIC数据
     * @param ticData TIC图表数据
     */
    void addTicData(TicChartData *ticData);

    /**
     * @brief 添加XIC数据
     * @param xicData XIC图表数据
     */
    void addXicData(XicChartData *xicData);

    /**
     * @brief 添加XIC数据但不立即显示（用于批量操作）
     * @param xicData XIC图表数据
     */
    void addXicDataSilent(XicChartData *xicData);

    /**
     * @brief 刷新所有待显示的XIC数据
     */
    void refreshPendingXicData();

    /**
     * @brief 开始批量添加模式（延迟坐标轴更新）
     */
    void beginBatchAdd();

    /**
     * @brief 结束批量添加模式（执行坐标轴更新）
     */
    void endBatchAdd();

    /**
     * @brief 添加TIC和XIC数据对
     * @param ticData TIC图表数据
     * @param xicData XIC图表数据
     */
    void addTicXicPair(TicChartData *ticData, XicChartData *xicData);

    /**
     * @brief 通过事件ID移除TIC和XIC数据对
     * @param eventId 事件ID
     * @return 是否成功移除
     */
    bool removeTicXicPairByEventId(int eventId);

    /**
     * @brief 仅移除TIC数据（保留XIC数据）
     * @param eventId 事件ID
     * @return 是否成功移除
     */
    bool removeTicDataByEventId(int eventId);

    /**
     * @brief 仅移除XIC数据（保留TIC数据）
     * @param eventId 事件ID
     * @return 是否成功移除
     */
    bool removeXicDataByEventId(int eventId);

    /**
     * @brief 清除所有TIC和XIC数据
     */
    void clearAllTicXicData();

    /**
     * @brief 获取TIC数据列表
     * @return TIC数据列表
     */
    QList<TicChartData *> getTicDataList() const;

    /**
     * @brief 获取XIC数据列表
     * @return XIC数据列表
     */
    QList<XicChartData *> getXicDataList() const;

    /**
     * @brief 通过事件ID获取TIC数据
     * @param eventId 事件ID
     * @return TIC数据，如果不存在返回nullptr
     */
    TicChartData *getTicDataByEventId(int eventId) const;

    /**
     * @brief 通过事件ID获取XIC数据
     * @param eventId 事件ID
     * @return XIC数据，如果不存在返回nullptr
     */
    XicChartData *getXicDataByEventId(int eventId) const;

public:
    /**
     * @brief 重写基类的删除方法，区分TIC和XIC删除
     * @param UniqueID 曲线唯一ID
     * @return 是否成功删除
     */
    bool RemoveLxChartDataByUniqueID(QString UniqueID) override;

    /**
     * @brief 重写基类的添加方法，允许同时添加TIC和XIC数据
     * @param chartData 图表数据
     */
    void AddLxChartData(LxChartData *chartData) override;

    /**
     * @brief 清除MASS加载状态标志（供外部调用）
     */
    static void clearMassLoadingFlag();

    /**
     * @brief 重置扫描模式限制（清空图表时调用）
     */
    void resetScanModeRestriction();

    /**
     * @brief 检查当前图表中的扫描模式一致性
     * @return true表示一致，false表示存在混合扫描模式
     */
    bool checkCurrentScanModeConsistency();

protected:
    /**
     * @brief 重写事件过滤器，处理双击事件
     * @param obj 事件对象
     * @param event 事件
     * @return 是否处理了事件
     */
    bool eventFilter(QObject *obj, QEvent *event) override;

private:
    /**
     * @brief 初始化图表
     */
    void initTicXicChart();

    /**
     * @brief 内部移除TIC数据方法（不发信号，避免递归）
     * @param compositeKey 复合键（文件路径_事件ID）
     * @return 是否成功移除
     */
    bool internalRemoveTicData(const QString &compositeKey);

    /**
     * @brief 内部移除XIC数据方法（不发信号，避免递归）
     * @param uniqueID XIC的UniqueID
     * @return 是否成功移除
     */
    bool internalRemoveXicData(const QString &uniqueID);

    // 颜色同步相关方法已移除，每个曲线使用自己的随机颜色

    /**
     * @brief 处理TIC曲线的双击事件（显示MASS数据）
     * @param mouseEvent 鼠标事件
     */
    void handleTicDoubleClick(QMouseEvent *mouseEvent);

    /**
     * @brief 连接PeakFind信号
     */
    void connectPeakFindSignals();

    /**
     * @brief 处理TIC/XIC寻峰完成信号
     * @param windowId 窗口ID
     */
    void onTicXicPeakFindCompleted(const QString &windowId);

    /**
     * @brief 对单个新增的曲线进行自动积分计算
     * @param chartData 新增的曲线数据
     */
    void performAutoIntegrationForNewCurve(LxChartData *chartData);

    /**
     * @brief 检查点击位置是否在TIC曲线上
     * @param pos 点击位置
     * @return 如果在TIC曲线上返回对应的TicChartData，否则返回nullptr
     */
    TicChartData *getTicDataAtPosition(const QPointF &pos);

    /**
     * @brief 为指定的TIC曲线显示MASS数据（只处理单个TIC曲线，避免重复处理）
     * @param ticData 要处理的TIC数据
     * @param dataPos 双击位置的数据坐标
     */
    void showMassChartForTic(TicChartData *ticData, const QPointF &dataPos);

    /**
     * @brief 添加XIC数据但不创建图例（XIC使用TIC的图例管理）
     * @param xicData XIC数据
     */
    void addXicDataWithoutLegend(XicChartData *xicData);

    /**
     * @brief 重写UpdateGlobalRange方法，正确处理TIC/XIC的可见性
     */
    void UpdateGlobalRange() override;

    /**
     * @brief 重写图例点击处理方法，实现只能有一个图例被选中
     * @param uniqueId 被点击的图例对应的曲线唯一ID
     */
    void handleLegendClicked(const QString &uniqueId) override;

    /**
     * @brief 重写图例同步方法，实现图例和曲线的双向联动
     * @param chartData 曲线数据
     * @param isSelected 是否选中
     */
    void syncLegendSelection(LxChartData *chartData, bool isSelected) override;

    /**
     * @brief 重写移动浏览方法，实现TIC的移动浏览功能
     */
    void browsePrevious() override;
    void browseNext() override;

    /**
     * @brief 重写getBgMassPointVec方法，只处理TIC曲线数据，过滤掉XIC曲线
     * @return 背景质谱数据x点数组，只包含TIC曲线的数据
     */
    QVector<std::tuple<QString, int, QVector<double>>> getBgMassPointVec() override;

    /**
     * @brief 检查并为新增的TIC计算平均质谱（如果已设置背景区域）
     * @param ticData 新增的TIC数据
     * @param retryCount 重试次数，用于避免无限递归
     */
    void checkAndCalculateAvgMassForNewTic(TicChartData *ticData, int retryCount = 0);

    /**
     * @brief 当背景区域变化时，清除所有平均质谱数据并重新计算
     */
    void onBackgroundAreaChanged();

private:
    // TIC和XIC数据的映射关系
    QMap<QString, TicChartData *> m_ticDataMap; // 复合键（文件路径_事件ID） -> TIC数据
    QMap<QString, XicChartData *> m_xicDataMap; // UniqueID -> XIC数据（支持多个XIC）

    // 防止在MASS加载过程中重复响应移动浏览按钮点击
    static bool s_isLoadingMass;

    // 颜色管理已移除，每个曲线使用自己的随机颜色

    /**
     * @brief 生成复合键
     * @param paramPath 文件路径
     * @param eventId 事件ID
     * @return 复合键字符串
     */
    QString generateCompositeKey(const QString &paramPath, int eventId) const;

    /**
     * @brief 检查扫描模式是否兼容
     * @param scanMode 要检查的扫描模式
     * @return true表示兼容，false表示不兼容
     */
    bool isScanModeCompatible(GlobalEnums::ScanMode scanMode) const;

    /**
     * @brief 设置允许的扫描模式（仅在第一个TIC时调用）
     * @param scanMode 扫描模式
     */
    void setAllowedScanMode(GlobalEnums::ScanMode scanMode);

    /**
     * @brief 显示扫描模式不兼容的警告对话框
     * @param currentMode 当前图表的扫描模式
     * @param newMode 尝试添加的扫描模式
     */
    void showScanModeWarning(GlobalEnums::ScanMode currentMode, GlobalEnums::ScanMode newMode) const;

    // 颜色同步标记已移除

    // 批量添加模式控制
    bool m_batchAddMode;        // 是否处于批量添加模式
    QTimer *m_batchUpdateTimer; // 批量更新定时器

    // 图例选中状态管理
    LxChartLegend *m_currentSelectedLegend = nullptr; // 当前选中的图例

    // 扫描模式限制管理
    GlobalEnums::ScanMode m_allowedScanMode = GlobalEnums::ScanMode::FullScan; // 允许的扫描模式
    bool m_scanModeSet = false;                                                // 是否已设置扫描模式（第一个TIC设置）

private slots:
    // 旧的TIC/XIC联合可见性管理槽函数已移除

signals:
    /**
     * @brief TIC和XIC数据对添加完成信号
     * @param eventId 事件ID
     */
    void ticXicPairAdded(int eventId);

    /**
     * @brief TIC和XIC数据对移除完成信号
     * @param eventId 事件ID
     */
    void ticXicPairRemoved(int eventId);

    /**
     * @brief TIC/XIC积分计算完成信号
     */
    void ticXicIntegrationCompleted();

    /**
     * @brief TIC数据被删除信号（需要联动删除MASS和XIC）
     * @param eventId 事件ID
     * @param paramPath 文件路径
     */
    void ticDataRemoved(int eventId, const QString &paramPath);

    /**
     * @brief XIC数据被删除信号（不影响TIC和MASS）
     * @param eventId 事件ID
     * @param paramPath 文件路径
     */
    void xicDataRemoved(int eventId, const QString &paramPath);

private slots:
};

#endif // LXTICXICCHART_H
