#include "lxmasschart.h"
#include <QDebug>

LxMassChart::LxMassChart(GlobalEnums::TrackType type, QWidget *parent) : LxChart(type, parent)
{
    connectAll();
}

void LxMassChart::getPeakVec(std::vector<Peak> peakVec)
{
    this->peakVec = peakVec;
}

void LxMassChart::connectAll()
{
    connect(&PeakFind::getInstance(), &PeakFind::sg_searchPeaksSuccess, this, [=](QString windowId)
            {
        if (windowId == this->getWindowId()) {
            // qDebug() << "寻峰完成，曲线数量:" << m_chartDataVec.size();

            int peakFoundCount = 0;
            int alreadyPeakedCount = 0;
            int totalCurves = m_chartDataVec.size();

            foreach (LxChartData *data, m_chartDataVec) {
                if (!data) {
                    qDebug() << "LxMassChart::connectAll: 跳过空数据指针";
                    continue;
                }

                if (data->getHasFindPeak()) {
                    alreadyPeakedCount++;
                    // qDebug() << "曲线已寻峰，ID:" << data->getUniqueID() << "，峰数量:" << data->peakVec.size();
                } else {
                    // 这种情况不应该发生，因为PeakFind已经设置了寻峰状态
                    qDebug() << "警告：曲线未寻峰，ID:" << data->getUniqueID();
                    peakFoundCount++;
                }
            }

            // qDebug() << "寻峰检查完成，总数:" << totalCurves << "，已寻峰:" << alreadyPeakedCount;

            // 先清除所有现有的峰标记图形项，避免残影
            clearAllPeakGraphicsItems();

            // 显示新的峰标记
            showPeaks();

            // qDebug() << "峰标记显示完成";
        } });
}

bool LxMassChart::eventFilter(QObject *obj, QEvent *event)
{
    // 处理双击事件
    if (obj == m_chartView->viewport() && event->type() == QEvent::MouseButtonDblClick)
    {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
        if (mouseEvent->button() == Qt::LeftButton)
        {
            QPointF clickPos = mouseEvent->pos();
            qDebug() << "LxMassChart::eventFilter: 检测到左键双击，位置:" << clickPos;

            // 获取图表的绘图区域
            QRectF plotArea = m_chart->plotArea();
            qDebug() << "LxMassChart::eventFilter: plotArea范围:" << plotArea;

            // 优先检查是否双击在坐标轴区域（重置功能）
            if (isInXAxisArea(clickPos))
            {
                qDebug() << "LxMassChart::eventFilter: 双击X轴区域，重置X轴";
                resetXAxis();
                // 🎯 X轴重置后更新顶点标签位置
                updateVertexLabelsPosition();
                return true; // 阻止事件继续传播
            }

            if (isInYAxisArea(clickPos))
            {
                qDebug() << "LxMassChart::eventFilter: 双击Y轴区域，重置Y轴";
                resetYAxis();
                // 🎯 Y轴重置后更新顶点标签位置
                updateVertexLabelsPosition();
                return true; // 阻止事件继续传播
            }

            // 检查是否双击在plotArea内部（MASS功能）
            if (plotArea.contains(clickPos))
            {
                qDebug() << "LxMassChart::eventFilter: 双击plotArea内部，处理MASS双击";
                handleMassDoubleClick(mouseEvent);
                return true; // 阻止事件继续传播
            }

            qDebug() << "LxMassChart::eventFilter: 双击位置不在有效区域内，忽略";
            return true; // 阻止事件继续传播
        }

        // 右键双击交给基类处理（其他功能）
        if (mouseEvent->button() == Qt::RightButton)
        {
            qDebug() << "LxMassChart::eventFilter: 右键双击，交给基类处理";
            return LxChart::eventFilter(obj, event);
        }

        // 其他双击按钮不处理
        qDebug() << "LxMassChart::eventFilter: 其他按钮双击，忽略";
        return true;
    }

    // 对于非双击事件，正常调用基类处理
    return LxChart::eventFilter(obj, event);
}

void LxMassChart::handleMassDoubleClick(QMouseEvent *mouseEvent)
{
    if (!mouseEvent)
    {
        qDebug() << "LxMassChart::handleMassDoubleClick: mouseEvent为空";
        return;
    }

    QPointF pos = mouseEvent->pos();

    // 检查图表是否有数据系列
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
    {
        qDebug() << "LxMassChart::handleMassDoubleClick: 图表无数据系列，跳过双击处理";
        return;
    }

    // 将屏幕坐标转换为数据坐标
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
    }

    if (defaultSeries)
    {
        QPointF dataPos = m_chart->mapToValue(pos, defaultSeries);
        // 调用专门的MASS双击处理方法，传递数据坐标
        showXicChartForAllMass(dataPos);
        qDebug() << "LxMassChart::handleMassDoubleClick: 双击MASS曲线，数据坐标:" << dataPos;
    }
    else
    {
        qDebug() << "LxMassChart::handleMassDoubleClick: 无法获取默认曲线进行坐标转换";
    }
}

void LxMassChart::showXicChartForAllMass(const QPointF &dataPos)
{
    QVector<std::tuple<QString, int, double>> xicRequests;

    qDebug() << "LxMassChart::showXicChartForAllMass: 🎯 开始单线XIC查找";
    qDebug() << "  双击数据坐标:" << dataPos;
    qDebug() << "  总MASS数量:" << m_chartDataVec.size();

    // 🎯 新逻辑：只查找被点击的棒子，实现单线XIC加载
    MassChartData *clickedMassData = nullptr;
    int clickedBarIndex = -1;
    QPointF selectedPoint;
    double minDistance = std::numeric_limits<double>::max();

    // 遍历所有MASS曲线，找到距离点击位置最近的棒子
    for (int i = 0; i < m_chartDataVec.size(); i++)
    {
        const auto chartData = m_chartDataVec.at(i);
        if (!chartData)
        {
            continue;
        }

        // 确保是MASS数据
        MassChartData *massData = qobject_cast<MassChartData *>(chartData);
        if (!massData)
        {
            continue;
        }

        QVector<QPointF> dataPoints = massData->getData();
        if (dataPoints.isEmpty())
        {
            qDebug() << "LxMassChart::showXicChartForAllMass: MASS曲线数据为空，跳过，事件ID:" << massData->getEventNum();
            continue;
        }

        // 根据数据类型选择不同的查找策略
        int index = -1;
        double currentDistance = std::numeric_limits<double>::max();

        if (massData->isMrmData())
        {
            // 🎯 修复：对于MRM垂直线条数据，基于分组X轴位置查找最近的垂直线
            double minDistance = std::numeric_limits<double>::max();
            int bestIndex = -1;

            // 遍历所有垂直线，基于分组X轴位置计算距离
            for (int i = 0; i < dataPoints.size(); ++i)
            {
                // 🎯 垂直线在X轴上的位置使用分组位置：MASS起始位置 + 棒子索引
                double lineXPosition = massData->getMassStartPosition() + i;
                double distance = qAbs(lineXPosition - dataPos.x());

                if (distance < minDistance)
                {
                    minDistance = distance;
                    bestIndex = i;
                }
            }

            if (bestIndex >= 0)
            {
                index = bestIndex;
                currentDistance = minDistance;
                qDebug() << "LxMassChart::showXicChartForAllMass: MRM垂直线模式，MASS" << i << "最近线条" << (massData->getMassStartPosition() + bestIndex)
                         << "，点击X:" << dataPos.x() << "，数组索引:" << bestIndex << "，m/z:" << dataPoints[index].x()
                         << "，X轴位置:" << (massData->getMassStartPosition() + bestIndex) << "，距离:" << currentDistance;
            }
            else
            {
                qDebug() << "LxMassChart::showXicChartForAllMass: MASS" << i << "没有找到任何MRM垂直线，数据点数:" << dataPoints.size();
            }
        }
        else
        {
            // 对于连线图，使用原有的findIndex方法
            QVector<double> xData;

            // 只提取x坐标数据用于findIndex
            for (int j = 0; j < dataPoints.size(); j++)
            {
                const auto point = dataPoints.at(j);
                xData.append(point.x());
            }

            // 使用findIndex快速找到最近x坐标的索引
            index = findIndex(xData, dataPos.x(), false);

            if (index >= 0)
            {
                // 对于连线图，计算点击位置与找到点的距离
                QPointF foundPoint = dataPoints[index];
                currentDistance = std::abs(dataPos.x() - foundPoint.x());
                qDebug() << "LxMassChart::showXicChartForAllMass: 连线图模式，MASS" << i << "使用findIndex找到点，距离:" << currentDistance;
            }
        }

        // 🎯 只记录最近的棒子，不立即添加XIC请求
        if (index >= 0 && currentDistance < minDistance)
        {
            minDistance = currentDistance;
            clickedMassData = massData;
            clickedBarIndex = index;
            selectedPoint = dataPoints[index];

            qDebug() << "LxMassChart::showXicChartForAllMass: 🎯 更新最近棒子";
            qDebug() << "  MASS索引:" << i << "，棒子索引:" << index;
            qDebug() << "  事件ID:" << massData->getEventNum() << "，TIC事件ID:" << massData->getTic_event_id();
            qDebug() << "  文件路径:" << massData->getParamPath();
            qDebug() << "  m/z值:" << selectedPoint.x() << "，强度:" << selectedPoint.y();
            qDebug() << "  X轴位置:" << (massData->isMrmData() ? (massData->getMassStartPosition() + index) : selectedPoint.x());
            qDebug() << "  距离:" << currentDistance;
        }
    }

    // 🎯 只为最近的棒子添加XIC请求，实现单线加载
    if (clickedMassData && clickedBarIndex >= 0)
    {
        qreal mzValue = selectedPoint.x();
        xicRequests.push_back(std::make_tuple(clickedMassData->getParamPath(), clickedMassData->getTic_event_id(), mzValue));

        qDebug() << "LxMassChart::showXicChartForAllMass: 🎯🎯🎯 最终选择结果 🎯🎯🎯";
        qDebug() << "  点击坐标:" << dataPos;
        qDebug() << "  选中MASS: 来自第" << (m_chartDataVec.indexOf(clickedMassData)) << "个MASS数据";
        qDebug() << "  选中棒子: 第" << clickedBarIndex << "个棒子";
        qDebug() << "  文件路径:" << clickedMassData->getParamPath();
        qDebug() << "  事件ID:" << clickedMassData->getEventNum() << "，TIC事件ID:" << clickedMassData->getTic_event_id();
        qDebug() << "  m/z值:" << mzValue << "，强度:" << selectedPoint.y();
        qDebug() << "  X轴显示位置:" << (clickedMassData->isMrmData() ? (clickedMassData->getMassStartPosition() + clickedBarIndex) : selectedPoint.x());
        qDebug() << "  最小距离:" << minDistance;
        qDebug() << "  MASS起始位置:" << clickedMassData->getMassStartPosition();
        qDebug() << "  MASS棒子总数:" << clickedMassData->getData().size();

        // 发送信号，只包含一个XIC请求
        emit sg_showMultipleXicChart(xicRequests);
    }
    else
    {
        qDebug() << "LxMassChart::showXicChartForAllMass: ❌ 没有找到任何有效的MASS数据点";
        qDebug() << "  clickedMassData:" << (clickedMassData ? "有效" : "无效");
        qDebug() << "  clickedBarIndex:" << clickedBarIndex;
        qDebug() << "  总MASS数量:" << m_chartDataVec.size();
    }
}

/**
 * @brief 处理图例点击事件，实现只能有一个图例被选中
 * @param uniqueId 被点击的图例对应的曲线唯一ID
 */
void LxMassChart::handleLegendClicked(const QString &uniqueId)
{
    qDebug() << "LxMassChart::handleLegendClicked: 图例被点击，UniqueID:" << uniqueId;

    // 查找被点击的图例对应的曲线数据
    LxChartData *clickedChartData = nullptr;
    LxChartLegend *clickedLegend = nullptr;

    for (LxChartData *chartData : m_chartDataVec)
    {
        if (chartData && chartData->getUniqueID() == uniqueId)
        {
            clickedChartData = chartData;
            clickedLegend = chartData->legend;
            break;
        }
    }

    if (!clickedChartData || !clickedLegend)
    {
        qDebug() << "LxMassChart::handleLegendClicked: 未找到对应的曲线数据或图例";
        return;
    }

    // 如果点击的是当前已选中的图例，则取消选中
    if (m_currentSelectedLegend == clickedLegend)
    {
        // 取消选中
        m_currentSelectedLegend->setChecked(false);
        m_currentSelectedLegend = nullptr;

        // 🎯 恢复曲线颜色（支持MRM多系列）
        if (lastSelectedChartData == clickedChartData)
        {
            QColor originalColor = lastSelectedChartData->getOriginalColor();
            QPen defaultPen = lastSelectedChartData->getDefaultPen();
            defaultPen.setColor(originalColor);

            // 🎯 如果是MRM数据，恢复所有棒子系列的颜色
            if (lastSelectedChartData->isMrmData() && !lastSelectedChartData->getMrmBarSeries().isEmpty())
            {
                for (QAbstractSeries *barSeries : lastSelectedChartData->getMrmBarSeries())
                {
                    QLineSeries *lineSeries = qobject_cast<QLineSeries *>(barSeries);
                    if (lineSeries)
                    {
                        lineSeries->setPen(defaultPen);
                        lineSeries->setColor(originalColor);
                    }
                }
                qDebug() << "LxMassChart::handleLegendClicked: 恢复MRM数据原始颜色:" << originalColor.name() << "，系列数量:" << lastSelectedChartData->getMrmBarSeries().size();
            }
            else
            {
                // 普通数据：恢复单个系列
                for (int i = 0; i < m_chart->series().size(); i++)
                {
                    QAbstractSeries *abstractSeries = m_chart->series().at(i);
                    if (abstractSeries->name() == lastSelectedChartData->getUniqueID())
                    {
                        QLineSeries *lineSeries = qobject_cast<QLineSeries *>(abstractSeries);
                        if (lineSeries)
                        {
                            lineSeries->setPen(defaultPen);
                            lineSeries->setColor(originalColor);
                        }
                        break;
                    }
                }
            }
            lastSelectedChartData = nullptr;
        }

        qDebug() << "LxMassChart::handleLegendClicked: 取消选中图例，UniqueID:" << uniqueId;
        return;
    }

    // 取消之前选中的图例
    if (m_currentSelectedLegend)
    {
        m_currentSelectedLegend->setChecked(false);
    }

    // 设置新的选中图例
    m_currentSelectedLegend = clickedLegend;
    m_currentSelectedLegend->setChecked(true);

    // 选中对应的曲线（调用基类的setSelectColor方法）
    LxChart::setSelectColor(clickedChartData);

    qDebug() << "LxMassChart::handleLegendClicked: 选中图例，UniqueID:" << uniqueId;
}

/**
 * @brief 重写图例同步方法，实现图例和曲线的双向联动
 * @param chartData 曲线数据
 * @param isSelected 是否选中
 */
void LxMassChart::syncLegendSelection(LxChartData *chartData, bool isSelected)
{
    if (!chartData || !chartData->legend)
        return;

    if (isSelected)
    {
        // 选中操作：取消之前选中的图例，设置新的选中图例
        if (m_currentSelectedLegend && m_currentSelectedLegend != chartData->legend)
        {
            m_currentSelectedLegend->setChecked(false);
        }

        m_currentSelectedLegend = chartData->legend;
        m_currentSelectedLegend->setChecked(true);

        qDebug() << "LxMassChart::syncLegendSelection: 选中图例，UniqueID:" << chartData->getUniqueID();
    }
    else
    {
        // 取消选中操作：如果是当前选中的图例，则取消选中
        if (m_currentSelectedLegend == chartData->legend)
        {
            m_currentSelectedLegend->setChecked(false);
            m_currentSelectedLegend = nullptr;
            qDebug() << "LxMassChart::syncLegendSelection: 取消选中图例，UniqueID:" << chartData->getUniqueID();
        }
    }
}
