#include "lxchartlegend.h"
#include "ui_lxchartlegend.h"
#include <QFile>
#include <QTextStream>
#include <QDebug>
#include <QFileDialog>
#include <QMessageBox>
#include <QStandardPaths>
#include "xlsxdocument.h"
#include "xlsxworksheet.h"

LxChartLegend::LxChartLegend(QString fileDataPath, QString title, QString color, QString UniqueID, QString toolTip, GlobalEnums::TrackType trackType,
                             QWidget *parent)
    : QWidget(parent), m_path(fileDataPath), m_title(title), m_color(color), m_toolTip(toolTip), UniqueID(UniqueID), m_trackType(trackType),
      ui(new Ui::LxChartLegend)
{
    ui->setupUi(this);

    ui->label_legend->setStyleSheet(QString("background-color: rgb(%1);").arg(m_color));

    ui->btn_name->setToolTip(title);
    ui->btn_name->setText(title);

    // 设置类型显示
    updateTypeDisplay();

    initMenu();
    installEventFilter(this);
    connectAll();

    // 加载LxChart专用样式
    loadLxChartStyles();
}
// 在LxChartLegend类中添加eventFilter方法
bool LxChartLegend::eventFilter(QObject *obj, QEvent *event)
{
    if (event->type() == QEvent::MouseButtonPress)
    {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
        if (mouseEvent->button() == Qt::RightButton)
        {
            deleteAction->setIcon(QIcon(":/Icons/QAction/delete.png"));
            if (m_bool_curveVisible)
            {
                hideAction->setIcon(QIcon(":/Icons/QAction/hide.png"));
                hideAction->setText(tr("隐藏"));
            }
            else
            {
                hideAction->setIcon(QIcon(":/Icons/QAction/show.png"));
                hideAction->setText(tr("显示"));
            }

            // 显示菜单
            contextMenu->exec(mouseEvent->globalPos());

            return true; // 截获事件
        }
    }

    if (event->type() == QEvent::Enter)
    {
        return true; // 截获事件
    }

    return QObject::eventFilter(obj, event);
}
LxChartLegend::~LxChartLegend()
{
    delete ui;
}

QString LxChartLegend::getColor() const
{
    return m_color;
}

void LxChartLegend::setColor(const QString &newColor)
{
    m_color = newColor;

    ui->label_legend->setStyleSheet(QString("background-color: %1;").arg(m_color));
    // qDebug() << ui->label_legend->styleSheet();
}

QString LxChartLegend::getToolTip() const
{
    return m_toolTip;
}

bool LxChartLegend::bool_curveVisible() const
{
    return m_bool_curveVisible;
}

QString LxChartLegend::getUniqueID() const
{
    return UniqueID;
}

QString LxChartLegend::path() const
{
    return m_path;
}

QString LxChartLegend::title() const
{
    return m_title;
}

void LxChartLegend::setTitle(const QString &newTitle)
{
    m_title = newTitle;
    ui->btn_name->setText(newTitle);
    ui->btn_name->setToolTip(newTitle);
    qDebug() << "LxChartLegend::setTitle: 更新标题为:" << newTitle << "，UniqueID:" << UniqueID;
}

// 类型管理方法实现
GlobalEnums::TrackType LxChartLegend::getTrackType() const
{
    return m_trackType;
}

void LxChartLegend::setTrackType(GlobalEnums::TrackType type)
{
    m_trackType = type;
    updateTypeDisplay();
}

void LxChartLegend::updateTypeDisplay()
{
    switch (m_trackType)
    {
    case GlobalEnums::TrackType::TIC:
    {
        ui->btn_type->setIcon(QIcon(":/Icons/LxChart/T.svg"));
        ui->btn_type->setToolTip(tr("TIC"));
        break;
    }
    case GlobalEnums::TrackType::XIC:
    {
        ui->btn_type->setIcon(QIcon(":/Icons/LxChart/X.svg"));
        ui->btn_type->setToolTip(tr("XIC"));

        break;
    }

    case GlobalEnums::TrackType::MS:
    {
        ui->btn_type->setIcon(QIcon(":/Icons/LxChart/M.svg"));
        ui->btn_type->setToolTip(tr("MASS"));

        break;
    }

    default:
        return;
    }
}

void LxChartLegend::initMenu()
{
    contextMenu = new QMenu(this);
    deleteAction = contextMenu->addAction("删除");
    hideAction = contextMenu->addAction("隐藏");
    colorAction = contextMenu->addAction("颜色");
    widthAction = contextMenu->addAction("宽度");
    fontAction = contextMenu->addAction("字体");
    exportDataAction = contextMenu->addAction("导出数据");
    colorAction->setIcon(QIcon(":/Icons/QAction/hue.png"));
    widthAction->setIcon(QIcon(":/Icons/QAction/lineWidth.png"));
    fontAction->setIcon(QIcon(":/Icons/QAction/font.png"));
    exportDataAction->setIcon(QIcon(":/Icons/QAction/export.png")); // 如果有导出图标的话
}

void LxChartLegend::connectAll()
{
    // 连接btn_name的点击事件
    connect(ui->btn_name, &QPushButton::clicked, [this]()
            { emit sg_legendClicked(UniqueID); });
    connect(fontAction, &QAction::triggered, [=]()
            {
        // 创建字体选择对话框
        QFont selectedFont = QFontDialog::getFont(nullptr, QFont(), this);
        // 检查用户是否选择了字体
        if (selectedFont != QFont()) {
            // 设置 QPushButton 的字体为选择的字体
            ui->btn_name->setFont(selectedFont);
        } });
    connect(colorAction, &QAction::triggered, [=]()
            {
        QColor color = QColorDialog::getColor(Qt::white, this, tr("请选择颜色"));
        if (color == QColor()) {
            return;
        }
        setColor(color.name());
        emit sg_changeColor(UniqueID, color); });

    connect(widthAction, &QAction::triggered, [=]()
            {
        int width = LineWidthDialog::setWidth();
        if (width == m_int_width) {
            return;
        }
        m_int_width = width;
        emit sg_changeLineWidth(UniqueID, width); });

    connect(deleteAction, &QAction::triggered, [this]()
            {
        qDebug() << "LxChartLegend: 准备删除，路径:" << m_path << "，UniqueID:" << UniqueID;
        emit sg_deleteLegend(UniqueID);
        qDebug() << "LxChartLegend: 删除信号已发射，路径:" << m_path; });
    connect(hideAction, &QAction::triggered, [=]()
            {
        m_bool_curveVisible = !m_bool_curveVisible;

        if (m_bool_curveVisible) {
            hideAction->setIcon(QIcon(":/Icons/QAction/hide.png"));
            hideAction->setText(tr("隐藏"));

        } else {
            hideAction->setIcon(QIcon(":/Icons/QAction/show.png"));
            hideAction->setText(tr("显示"));
        }

        emit sg_changeVisible(UniqueID, m_bool_curveVisible); });

    // 连接导出数据动作
    connect(exportDataAction, &QAction::triggered, this, &LxChartLegend::exportData);
}

void LxChartLegend::exportData()
{
    // 检查当前图例是否被选中
    if (!m_checked)
    {
        QMessageBox::warning(this, "提示", "请先选中此曲线再进行导出操作。");
        return;
    }

    // 发射导出数据信号，让LxChart处理具体的导出逻辑
    emit sg_exportData(UniqueID);
}

void LxChartLegend::loadLxChartStyles()
{
    // 使用LxChartLegend专用的QSS文件
    QString possiblePath = ":/QssFiles/LxChartLegend_Style.qss"; // 专用资源文件路径

    QString styleSheet;
    QString usedPath;

    // 尝试加载QSS文件
    QFile file(possiblePath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QTextStream stream(&file);
        styleSheet = stream.readAll();
        usedPath = possiblePath;
        file.close();
    }

    if (!styleSheet.isEmpty())
    {
        // 应用样式到当前控件
        this->setStyleSheet(styleSheet);
        qDebug() << "LxChartLegend专用样式加载成功，路径:" << usedPath;
    }
    else
    {
        qWarning() << "LxChartLegend专用样式文件未找到，尝试的路径:" << possiblePath;
    }
}

// 选中状态管理方法
bool LxChartLegend::isChecked() const
{
    return m_checked;
}

void LxChartLegend::setChecked(bool checked)
{
    if (m_checked != checked)
    {
        m_checked = checked;
        updateSelectedStyle();
        qDebug() << "LxChartLegend::setChecked: 设置选中状态为" << checked << "，UniqueID:" << UniqueID;
    }
}

void LxChartLegend::updateSelectedStyle()
{
    if (m_checked)
    {
        // 设置选中状态的样式
        ui->btn_name->setStyleSheet(
            "QPushButton#btn_name {"
            "    border: 2px solid #0078d7;"
            "    background-color: rgba(0, 120, 215, 0.1);"
            "    color: #0078d7;"
            "    font-weight: bold;"
            "    border-radius: 3px;"
            "}");
    }
    else
    {
        // 恢复默认样式
        ui->btn_name->setStyleSheet("");
    }
}
