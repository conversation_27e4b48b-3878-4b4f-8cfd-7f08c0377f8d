#ifndef DATAPROCESSOR_H
#define DATAPROCESSOR_H

#include <QObject>
#include <QVector>
#include <algorithm>

class DataProcessor : public QObject
{
    Q_OBJECT

public:
    DataProcessor(QObject *parent = nullptr);
    ~DataProcessor();

    // 处理TIC数据
    void processTICData(const QVector<double> &rawTimePoints, const QVector<double> &rawIntensities, QVector<double> &processedTimePoints,
                        QVector<double> &processedIntensities);

    // 处理质谱数据
    void processMassData(const QVector<double> &rawMassToCharge, const QVector<double> &rawIntensities, QVector<double> &processedMassToCharge,
                         QVector<double> &processedIntensities);

private:
    // 数据平滑函数
    void smoothData(const QVector<double> &input, QVector<double> &output, int windowSize = 5);
};

#endif // DATAPROCESSOR_H
