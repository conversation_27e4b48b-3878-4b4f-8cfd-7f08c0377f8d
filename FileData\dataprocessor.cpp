#include "dataprocessor.h"

DataProcessor::DataProcessor(QObject *parent) : QObject(parent)
{
}

DataProcessor::~DataProcessor()
{
}

void DataProcessor::processTICData(const QVector<double> &rawTimePoints, const QVector<double> &rawIntensities, QVector<double> &processedTimePoints,
                                   QVector<double> &processedIntensities)
{
    if (rawTimePoints.isEmpty() || rawIntensities.isEmpty()) {
        return;
    }

    // 复制原始时间点
    processedTimePoints = rawTimePoints;

    // 对强度数据进行平滑处理
    // smoothData(rawIntensities, processedIntensities);
}

void DataProcessor::processMassData(const QVector<double> &rawMassToCharge, const QVector<double> &rawIntensities, QVector<double> &processedMassToCharge,
                                    QVector<double> &processedIntensities)
{
    if (rawMassToCharge.isEmpty() || rawIntensities.isEmpty()) {
        return;
    }

    // 复制原始质荷比数据
    processedMassToCharge = rawMassToCharge;

    // 对强度数据进行处理
    smoothData(rawIntensities, processedIntensities, 3);
}

void DataProcessor::smoothData(const QVector<double> &input, QVector<double> &output, int windowSize)
{
    output.clear();
    int size = input.size();
    if (size == 0)
        return;

    // 简单的移动平均平滑
    for (int i = 0; i < size; ++i) {
        double sum = 0.0;
        int count = 0;

        // 求窗口内点的平均值
        for (int j = std::max(0, i - windowSize / 2); j <= std::min(size - 1, i + windowSize / 2); ++j) {
            sum += input[j];
            count++;
        }

        output.append(sum / count);
    }
}
