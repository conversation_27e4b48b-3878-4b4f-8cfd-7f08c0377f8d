#include "mainwindow.h"

#include <QApplication>
#include <QCommandLineParser>
#include <QDebug>
#include <QTextCodec>
#include "Globals/GlobalDefine.h"

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // 🎯 解决中文乱码问题
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));

#ifdef _WIN32
    // Windows控制台UTF-8编码设置
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif

    // qDebug() << "应用程序启动，主线程:" << QThread::currentThread();

    // 初始化全局分析配置（模拟示例项目的配置加载）
    GlobalDefine::GlobalAnalysisConfig::getInstance();
    // qDebug() << "全局分析配置初始化完成";

    // 正常启动应用程序
    MainWindow w;
    w.show();

    int result = a.exec();
    qDebug() << "应用程序退出，返回值:" << result;

    return result;
}
