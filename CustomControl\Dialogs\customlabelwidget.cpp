#include "customlabelwidget.h"
#include "ui_customlabelwidget.h"

CustomLabelWidget::CustomLabelWidget(QString content, QFont font, QColor color, QWidget *parent)
    : QWidget(parent), m_qstr_content(content), font(font), color(color), ui(new Ui::CustomLabelWidget)
{
    ui->setupUi(this);
    ui->label->setText(m_qstr_content);
    ui->label->setFont(font);
    ui->label->setStyleSheet(QString("color: %1;").arg(color.name())); // 使用 QColor 的 name() 方法获取颜色的十六进制表示

    connectAll();
}

void CustomLabelWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        lastPos = event->pos();
        m_bool_isDragging = true;
        // qDebug() << "左键点击" << lastPos << mapToGlobal(getPoint().toPoint());
        // 发送信号或执行其他操作
        // emit rightClicked(); // 假设你有一个右键点击的信号
        event->accept(); // 接受事件，防止进一步传播
    } else {
        QWidget::mousePressEvent(event); // 处理其他鼠标事件
    }
}

void CustomLabelWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (m_bool_isDragging && (event->buttons() & Qt::LeftButton)) {
        QPoint delta = event->pos() - lastPos;
        move(pos() + delta);

        // 发送标签移动信号，用于更新数据坐标
        emit labelMoved(this);
    }
}

// bool CustomLabelWidget::eventFilter(QObject *obj, QEvent *event)
// {
//     switch (event->type()) {
//     case QEvent::MouseButtonPress: {
//         QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);

//         if (mouseEvent->button() == Qt::RightButton) {
//             qDebug() << "发送信号";
//             return true;
//         }
//     }
//     }

//     // 其他事件放行，让后面的 filter 或目标对象自己接收
//     return false;
// }
CustomLabelWidget::~CustomLabelWidget()
{
    delete ui;
}

QPointF CustomLabelWidget::getPoint() const
{
    return point;
}

void CustomLabelWidget::setPoint(QPointF newPoint)
{
    point = newPoint;
}

void CustomLabelWidget::connectAll()
{
    connect(ui->btn_hide, &QPushButton::clicked, [=]() { hideLabel(); });
    connect(ui->btn_delete, &QPushButton::clicked, [=]() { emit deleteLabel(); });
}

void CustomLabelWidget::enterEvent(QEvent *e)
{
    // qDebug() << "鼠标进入";
    m_bool_isHover = true;
}

void CustomLabelWidget::leaveEvent(QEvent *e)
{
    // qDebug() << "鼠标离开";
    m_bool_isHover = false;
}

void CustomLabelWidget::showLabel()
{
    // 显示标注区域，隐藏标记点
    if (m_marker) {
        m_marker->setVisible(false);
    }
    show();
    emit showLabelRequested(this);
}

void CustomLabelWidget::hideLabel()
{
    // 隐藏标注区域，显示标记点
    if (m_marker) {
        m_marker->setVisible(true);
    }
    hide();
    emit hideLabelRequested(this);
}

QGraphicsEllipseItem *CustomLabelWidget::getMarker() const
{
    return m_marker;
}

void CustomLabelWidget::setMarker(QGraphicsEllipseItem *marker)
{
    m_marker = marker;
}

bool CustomLabelWidget::isLabelVisible() const
{
    return isVisible();
}
