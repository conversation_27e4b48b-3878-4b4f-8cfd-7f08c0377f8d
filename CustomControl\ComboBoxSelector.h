#ifndef COMBOBOXSELECTOR_H
#define COMBOBOXSELECTOR_H

#include <QComboBox>
#include <QMouseEvent>

class ComboBoxSelector : public QComboBox
{
    Q_OBJECT

public:
    explicit ComboBoxSelector(QWidget *parent = nullptr);
    ~ComboBoxSelector();

signals:
    // 自定义信号，当用户点击下拉项时触发，但不改变当前选中项
    void itemClicked(int index);

protected:
    // 重写showPopup方法，确保展示下拉列表
    void showPopup() override;
    
private slots:
    // 处理用户点击下拉项的槽函数
    void handleItemActivated(int index);
};

#endif // COMBOBOXSELECTOR_H