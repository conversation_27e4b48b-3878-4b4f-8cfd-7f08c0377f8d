#ifndef PEAKFIND_H
#define PEAKFIND_H

#include <QObject>
#include "FileData/taskmanager.h"
#include "wh_peakSearch.h"
#include "LxChart/lxchart.h"
#include "wh_signal_smothing.h"
#include "wh_baselineCorrect.h"
#include "Config/PeakFindingParams.h"
class PeakFind : public QObject
{
    Q_OBJECT
public:
    // 获取单例实例
    static PeakFind &getInstance()
    {
        static PeakFind instance; // 饿汉式单例
        return instance;
    }
    static void searchPeaksWithDefaultParams(LxChart *chart);
    static void serachPeaks(LxChart *chart, uint smoothType, uint BLineCorrect, uint slope, uint area, uint height);

    // 新增：使用配置参数的寻峰方法
    static void searchPeaksWithConfig(LxChart *chart, const PeakFindingConfig &config, bool forceReprocess = false);
signals:
    void sg_searchPeaksSuccess(QString windowId);

private:
    explicit PeakFind(QObject *parent = nullptr);

    // 删除拷贝构造函数和赋值运算符
    PeakFind(const PeakFind &) = delete;
    PeakFind &operator=(const PeakFind &) = delete;
};

#endif // PEAKFIND_H
