# 质谱数据文件读取核心分析

## 1. 数据结构概述

### 1.1 文件结构

质谱数据文件主要分为两种：

- **Param/P 文件**：包含参数信息和 TIC 数据
- **Dat/D 文件**：包含原始质谱数据

### 1.2 核心数据结构

#### 文件头结构

```cpp
struct _StreamHead {
    uint32_t length;      // 头部总长度
    uint32_t dateTime;    // 时间戳
    // 其他参数...
};
```

#### 段结构 (Segment)

```cpp
struct _Segment {
    Type_Segment type;     // 段类型
    uint32_t lengthReserved; // 保留长度
    uint32_t countsEvent;  // 事件数量
    uint32_t lengthEvent;  // 事件长度
    uint32_t length;       // 总长度
    _Event fisrtEvent;     // 第一个事件
};
```

#### 事件结构 (Event)

```cpp
// 基础事件结构
struct _Event {
    Type_Event type;       // 事件类型
    double holdTimeMs;     // 保持时间(ms)
    // 其他参数...
};

// SIM事件结构
struct _EventSIM : _Event {
    char title[30];        // 标题
    double holdTimeMs;     // 保持时间
    double msPrecursor;    // 前体离子质量
    double mass[64];       // 质量数组
    double time[64];       // 时间数组
};

// 扫描事件结构
struct _EventScan : _Event {
    char title[30];        // 标题
    double msStart;        // 起始质量
    double msEnd;          // 结束质量
    double holdTimeMs;     // 保持时间
};
```

#### XIC 数据结构

```cpp
struct _PARAM_XIC {
    double massRange;      // 质量范围
    QColor color;          // 颜色
    double mass;           // 质量
    double intensity;      // 强度
    std::vector<double> yListXIC; // XIC数据列表
};
```

## 2. 数据读取流程

### 2.1 TIC 数据读取流程

1. **调用入口**：`cDataFileRead::loadFileTIC()`
2. **内部实现**：`loadDataHead()`
3. **主要步骤**：
   - 读取文件头 (`_StreamHead`)
   - 解析 XIC 参数
   - 读取 TIC 数据
   - 根据 Period 参数进行分页处理
   - 返回索引数组、时间轴数据、强度数据等

### 2.2 MASS 数据读取流程

1. **调用入口**：`cDataFileRead::loadFileMass()`
2. **内部实现**：`loadDataBody()`
3. **主要步骤**：
   - 根据帧索引定位数据
   - 读取数据体 (`_StreamBody`)
   - 根据数据类型进行解压缩和转换
   - 返回原始质谱数据

### 2.3 数据解析流程

1. **调用入口**：`dataDisassembleFirst()`
2. **主要步骤**：
   - 解析段结构 (`_Segment`)
   - 遍历所有事件
   - 根据事件类型调用不同的解析函数：
     - SIM 事件：`disassembleSIM()`
     - 扫描事件：`disassembleScan()`
     - MRM 事件：特定处理
   - 返回解析后的数据

## 3. Segment 和 Event 关系

你的理解是正确的。一个文件内包含多个 Segment，每个 Segment 包含多个 Event。

### 3.1 数据组织方式

1. **Segment（段）**：

   - 时间上的分割单位
   - 每个 Segment 包含多个 Event
   - 通过`_Segment`结构体定义

2. **Event（事件）**：

   - 不同的数据采集模式
   - 类型包括：SIM、Scan、MRM 等
   - 每个 Event 对应一条曲线
   - 通过`_Event`及其派生结构体定义

3. **Experiment（实验）**：
   - 由多个 Event 组成
   - 跨越多个 Segment
   - 需要遍历所有 Segment 并重组 Event 数据

### 3.2 数据重组过程

以你的图示为例：

```
Expriment1:event1
|-----------|
|           |
Seg1       Seg2

Expriment2:event2
      |-----------|
      |           |
    Seg2         Seg3

Expriment2:event3
            |-----------|
            |           |
          Seg3         Seg4
```

处理流程：

1. 获取所有 Segment 和 Event 信息
2. 对于每个 Experiment：

   - 确定包含的 Event 类型
   - 遍历所有 Segment
   - 检查每个 Segment 是否包含目标 Event
   - 提取并拼接数据

3. 例如处理 Experiment2：
   - 包含 event2 和 event3
   - 遍历 Seg1-4
   - Seg1 不包含 event2/3，跳过
   - Seg2 包含 event2，提取数据
   - Seg3 包含 event2 和 event3，提取数据
   - Seg4 包含 event3，提取数据
   - 分别拼接 event2 和 event3 的数据，形成两条独立曲线

## 4. 核心函数分析

### 4.1 `loadFileTIC` - TIC 数据加载

```cpp
bool cDataFileRead::loadFileTIC(
    double Period,                // 分页周期
    std::vector<qint64>& pIndexArray,  // 帧索引数组
    std::vector<double>& pTicX,   // TIC时间轴
    std::vector<double>& pTicY,   // TIC强度值
    QMap<uint32_t, QMap<QString, _PARAM_XIC*>>& pXIC, // XIC结构
    QList<std::vector<double>>& pStructLines, // 其他数据线
    QByteArray& pStreamHead,      // 文件头
    QVector<qint64>& pPageTIC,    // 分页信息
    QString pFilePathName         // 文件路径
)
```

功能：

- 加载 TIC 数据和相关参数
- 支持 XIC 数据结构
- 处理分页逻辑

### 4.2 `loadFileMass` - 质谱数据加载

```cpp
bool cDataFileRead::loadFileMass(
    int index,                    // 帧索引
    const std::vector<qint64>& pIndexArray, // 索引数组
    QByteArray& pData,            // 输出数据
    QByteArray& pStreamBody,      // 数据体
    QString pFilePathName         // 文件路径
)
```

功能：

- 加载特定帧的质谱数据
- 处理数据解压缩和转换

### 4.3 `dataDisassembleFirst` - 数据解析

```cpp
uint32_t dataDisassembleFirst(
    QByteArray& pByteArray,       // 原始数据
    cParamValue::_Segment* pSegment, // 段结构
    QList<std::vector<double>>& pListX, // X轴数据列表
    QList<std::vector<double>>& pListY, // Y轴数据列表
    QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>>& pSTRUCT_DATA, // 事件数据
    bool restart                  // 是否重新开始
)
```

功能：

- 解析原始数据
- 根据事件类型分发处理
- 填充 X/Y 轴数据

## 5. 实际应用示例

### 5.1 加载 TIC 数据

```cpp
// 初始化参数
std::vector<qint64> indexArray;
std::vector<double> ticX, ticY;
QMap<uint32_t, QMap<QString, _PARAM_XIC*>> xicMap;
QList<std::vector<double>> otherLines;
QByteArray streamHead;
QVector<qint64> pageTIC;

// 加载TIC数据
bool success = cDataFileRead::loadFileTIC(
    period,
    indexArray,
    ticX,
    ticY,
    xicMap,
    otherLines,
    streamHead,
    pageTIC,
    filePath
);
```

### 5.2 加载 MASS 数据并解析

```cpp
// 加载特定帧的MASS数据
QByteArray massData, streamBody;
bool success = cDataFileRead::loadFileMass(
    frameIndex,
    indexArray,
    massData,
    streamBody,
    datFilePath
);

// 解析数据
QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> structData;
QList<std::vector<double>> massX, massY;
dataDisassembleFirst(
    massData,
    segment,
    massX,
    massY,
    structData,
    true
);

// 处理每个事件的数据
for (int i = 0; i < structData.size(); i++) {
    // 根据事件类型处理数据
    if (structData[i].first == cParamValue::Type_SIM) {
        // 处理SIM数据
    } else if (structData[i].first == cParamValue::Type_Scan) {
        // 处理Scan数据
    }
}
```

## 6. 数据校准与处理

### 6.1 质量校准

质谱数据通常需要进行质量校准，以确保质量数值的准确性：

```cpp
// 质量校准函数
static void calibrationF(_CONGIG_OMS::_PARAM_FIT& pPARAM_FIT,
                         std::vector<double>& pBuffer,
                         cParamValue::_EventLIT* pEventLIT,
                         _CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA)
{
    // 计算延迟点、预读点和后读点
    tempSTRUCT_DATA->uDelayPoint = tempSTRUCT_DATA->uEvtValidPoint *
            pEventLIT->delayTimeMs / pEventLIT->holdTimeMs;

    // 计算质量范围和步长
    double tempStart = pEventLIT->msStart <= pEventLIT->msEnd ?
                pEventLIT->msStart - pEventLIT->msPrecursor :
                pEventLIT->msStart + pEventLIT->msPrecursor;
    double tempEnd = pEventLIT->msStart <= pEventLIT->msEnd ?
                pEventLIT->msEnd + pEventLIT->msPrecursor :
                pEventLIT->msEnd - pEventLIT->msPrecursor;
    double dbStep = (tempEnd - tempStart) / (tempSTRUCT_DATA->uEventPoint-1);

    // 应用校准多项式
    if(pPARAM_FIT.enable == 1) {
        // 根据多项式系数进行校准
        // 支持2-6阶多项式
    }
}
```

### 6.2 峰检测与分析

数据加载后，通常需要进行峰检测和分析：

```cpp
bool AnalyzeScan(std::vector<double>& tempX,
                 std::vector<double>& tempY,
                 _CONGIG_OMS::_STRUCT_DATA& pSTRUCT_DATA,
                 _STRUCT_PEAK& pSTRUCT_PEAK)
{
    // 峰检测
    mSignalProcessing.CentroidData(tempX, tempY,
                                  pSTRUCT_PEAK.Absc,
                                  pSTRUCT_PEAK.Ord,
                                  pSTRUCT_PEAK.Start,
                                  pSTRUCT_PEAK.End,
                                  pSTRUCT_PEAK.Area);

    // 数据排序
    cDataSort::DateSort(pSTRUCT_PEAK.Absc, pSTRUCT_PEAK.Ord);

    // 计算半高峰宽
    vector<double> cHHPW = mSignalProcessing.GetHHPW(tempX, tempY,
                                                    pSTRUCT_PEAK.Start,
                                                    pSTRUCT_PEAK.End);

    // 生成峰标记
    for(uint32_t jj = 0; jj < uWidth; jj++) {
        strMarker[jj] = QString::number(cHHPW[jj]) + "\n" +
                       QString::number(pSTRUCT_PEAK.Absc[jj]) + "," +
                       QString::number(pSTRUCT_PEAK.Area[jj]);
    }
}
```

## 7. 多文件处理

新的解析代码支持多文件同时处理：

```cpp
int loadFileThreadTIC()
{
    int numFile = currentFileTIC().size();
    if(numFile < 1)
        return -2;

    // 清空数据容器
    mIndexArray.clear();
    mDataTIC_X.clear();
    mDataTIC_Y.clear();

    // 循环处理每个文件
    for(int indexFile = 0; indexFile < numFile; ++indexFile) {
        std::vector<qint64> IndexArray;
        std::vector<double> DataTIC_X;
        std::vector<double> DataTIC_Y;

        // 根据文件数量选择不同的加载方式
        if(numFile == 1) {
            // 单文件模式：加载XIC数据
            cDataFileRead::loadFileTIC(..., tempMap, ...);
        } else {
            // 多文件模式：不加载XIC数据
            cDataFileRead::loadFileTIC(...);
        }

        // 存储数据
        mIndexArray << IndexArray;
        mDataTIC_X << DataTIC_X;
        mDataTIC_Y << DataTIC_Y;
    }

    // 更新UI
    emit sUpdateGraphTIC(true);
}
```

## 8. 总结

质谱数据文件的读取是一个多层次的过程，涉及文件头解析、数据提取、事件分类和数据重组。核心是理解 Segment、Event 和 Experiment 之间的关系，以及如何从分散的数据中重建完整的曲线。

你的理解是完全正确的：

1. **一个文件包含多个 Segment**：Segment 是时间上的分割单位。

2. **一个 Experiment 可以包含多个 Event**：每个 Event 对应一条曲线。

3. **Event 被 Segment 分割**：需要遍历所有 Segment，查找对应的 Event，并将它们拼接成完整的曲线。

4. **数据重组过程**：
   - 获取 Experiment 中的所有 Event
   - 遍历所有 Segment
   - 在每个 Segment 中查找对应的 Event
   - 提取数据并按 Event 类型拼接
   - 每个 Event 独立形成一条曲线

这种设计允许灵活的数据采集方案，可以在一次实验中同时采集多种类型的数据（如 SIM 和 Scan），并在后续分析中分别处理。
